<template>
  <div v-if="asset">
    <a-row :gutter="16">
      <!-- 基本信息卡片 -->
      <a-col :span="16">
        <a-card title="基本信息" style="margin-bottom: 16px;">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="资产编号">
              {{ asset.asset_number }}
            </a-descriptions-item>
            <a-descriptions-item label="资产名称">
              {{ asset.name }}
            </a-descriptions-item>
            <a-descriptions-item label="分类">
              {{ asset.category_name }}
            </a-descriptions-item>
            <a-descriptions-item label="品牌">
              {{ asset.brand || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="型号">
              {{ asset.model || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="序列号">
              {{ asset.serial_number || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="getStatusColor(asset.status)">
                {{ asset.status_display }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="位置">
              {{ asset.location_name || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="责任人">
              {{ asset.responsible_person_name || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="采购日期">
              {{ asset.purchase_date || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="采购价格">
              <span v-if="asset.purchase_price">
                ¥{{ Number(asset.purchase_price).toLocaleString() }}
              </span>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="当前价值">
              <span v-if="asset.current_value">
                ¥{{ Number(asset.current_value).toLocaleString() }}
              </span>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="创建时间" :span="2">
              {{ formatDateTime(asset.created_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间" :span="2">
              {{ formatDateTime(asset.updated_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="描述" :span="2">
              {{ asset.description || '-' }}
            </a-descriptions-item>
          </a-descriptions>
          
          <div style="margin-top: 16px;">
            <a-space>
              <a-button type="primary" @click="$router.push(`/assets/${asset.id}/edit`)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button @click="showTransferModal">
                <SwapOutlined />
                转移
              </a-button>
              <a-button @click="$router.push('/assets')">
                返回列表
              </a-button>
            </a-space>
          </div>
        </a-card>
      </a-col>
      
      <!-- 资产图片 -->
      <a-col :span="8">
        <a-card title="资产图片" style="margin-bottom: 16px;">
          <div v-if="asset.image" style="text-align: center;">
            <a-image
              :src="asset.image"
              style="max-width: 100%; border-radius: 8px;"
            />
          </div>
          <div v-else style="text-align: center; padding: 40px 0; color: #bfbfbf;">
            <FileImageOutlined style="font-size: 48px;" />
            <div style="margin-top: 8px;">暂无图片</div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 维修记录 -->
    <a-card title="维修记录" style="margin-bottom: 16px;">
      <a-table
        :columns="maintenanceColumns"
        :data-source="maintenanceRecords"
        :loading="maintenanceLoading"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'maintenance_type'">
            <a-tag>{{ record.maintenance_type_display }}</a-tag>
          </template>
          <template v-else-if="column.key === 'cost'">
            <span v-if="record.cost">
              ¥{{ Number(record.cost).toLocaleString() }}
            </span>
            <span v-else>-</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 转移记录 -->
    <a-card title="转移记录">
      <a-table
        :columns="transferColumns"
        :data-source="transferRecords"
        :loading="transferLoading"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'transfer_info'">
            <div>
              <div v-if="record.from_location_name || record.to_location_name">
                位置: {{ record.from_location_name || '-' }} → {{ record.to_location_name || '-' }}
              </div>
              <div v-if="record.from_person_name || record.to_person_name">
                责任人: {{ record.from_person_name || '-' }} → {{ record.to_person_name || '-' }}
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 转移模态框 -->
    <a-modal
      v-model:open="transferModalVisible"
      title="资产转移"
      @ok="handleTransfer"
      @cancel="transferModalVisible = false"
    >
      <a-form :model="transferForm" layout="vertical">
        <a-form-item label="目标位置">
          <a-select v-model:value="transferForm.to_location" placeholder="选择目标位置">
            <a-select-option
              v-for="location in locations"
              :key="location.id"
              :value="location.id"
            >
              {{ location.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="新责任人">
          <a-select v-model:value="transferForm.to_person" placeholder="选择新责任人">
            <a-select-option
              v-for="user in users"
              :key="user.id"
              :value="user.id"
            >
              {{ user.username }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="转移日期">
          <a-date-picker
            v-model:value="transferForm.transfer_date"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="转移原因">
          <a-textarea
            v-model:value="transferForm.reason"
            placeholder="请输入转移原因"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAssetStore } from '@/stores/asset'
import { maintenanceAPI, transferAPI, locationAPI } from '@/services/api'
import { EditOutlined, SwapOutlined, FileImageOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const assetStore = useAssetStore()

const asset = ref(null)
const maintenanceRecords = ref([])
const transferRecords = ref([])
const locations = ref([])
const users = ref([])

const maintenanceLoading = ref(false)
const transferLoading = ref(false)

const transferModalVisible = ref(false)
const transferForm = ref({
  to_location: undefined,
  to_person: undefined,
  transfer_date: dayjs(),
  reason: ''
})

const maintenanceColumns = [
  {
    title: '维修类型',
    key: 'maintenance_type'
  },
  {
    title: '维修日期',
    dataIndex: 'maintenance_date',
    key: 'maintenance_date'
  },
  {
    title: '费用',
    key: 'cost'
  },
  {
    title: '技术员',
    dataIndex: 'technician',
    key: 'technician'
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  }
]

const transferColumns = [
  {
    title: '转移日期',
    dataIndex: 'transfer_date',
    key: 'transfer_date'
  },
  {
    title: '转移信息',
    key: 'transfer_info'
  },
  {
    title: '转移原因',
    dataIndex: 'reason',
    key: 'reason'
  },
  {
    title: '操作人',
    dataIndex: 'created_by_name',
    key: 'created_by_name'
  }
]

const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    inactive: 'orange',
    maintenance: 'red',
    scrapped: 'default'
  }
  return colors[status] || 'default'
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const showTransferModal = () => {
  transferForm.value = {
    to_location: undefined,
    to_person: undefined,
    transfer_date: dayjs(),
    reason: ''
  }
  transferModalVisible.value = true
}

const handleTransfer = async () => {
  try {
    const data = {
      ...transferForm.value,
      transfer_date: transferForm.value.transfer_date.format('YYYY-MM-DD')
    }
    
    await assetStore.transferAsset(asset.value.id, data)
    message.success('转移成功')
    transferModalVisible.value = false
    
    // 重新加载资产信息和转移记录
    await loadAssetData()
    await fetchTransferRecords()
  } catch (error) {
    message.error('转移失败')
  }
}

const loadAssetData = async () => {
  try {
    asset.value = await assetStore.fetchAsset(route.params.id)
  } catch (error) {
    message.error('加载资产信息失败')
    router.push('/assets')
  }
}

const fetchMaintenanceRecords = async () => {
  try {
    maintenanceLoading.value = true
    const response = await maintenanceAPI.getMaintenanceRecords({
      asset: route.params.id
    })
    maintenanceRecords.value = response.results || []
  } catch (error) {
    console.error('获取维修记录失败:', error)
  } finally {
    maintenanceLoading.value = false
  }
}

const fetchTransferRecords = async () => {
  try {
    transferLoading.value = true
    const response = await transferAPI.getTransferRecords({
      asset: route.params.id
    })
    transferRecords.value = response.results || []
  } catch (error) {
    console.error('获取转移记录失败:', error)
  } finally {
    transferLoading.value = false
  }
}

const fetchLocations = async () => {
  try {
    const response = await locationAPI.getLocations()
    locations.value = response.results || []
  } catch (error) {
    console.error('获取位置失败:', error)
  }
}

onMounted(() => {
  loadAssetData()
  fetchMaintenanceRecords()
  fetchTransferRecords()
  fetchLocations()
})
</script>
