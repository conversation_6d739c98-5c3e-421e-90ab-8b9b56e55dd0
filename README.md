# Django 资产管理系统

一个基于 Django + Vue.js + Ant Design Vue 的现代化资产管理系统。

## 功能特性

### 后端功能
- ✅ 资产管理（增删改查）
- ✅ 分类管理
- ✅ 位置管理
- ✅ 维修记录管理
- ✅ 资产转移记录
- ✅ 统计报表和仪表板
- ✅ RESTful API
- ✅ 数据过滤和搜索
- ✅ 分页支持

### 前端功能
- ✅ 现代化响应式界面
- ✅ 资产列表和详情页面
- ✅ 资产创建和编辑表单
- ✅ 图片上传支持
- ✅ 高级搜索和筛选
- ✅ 数据统计仪表板
- ✅ 资产转移功能
- ✅ 维修记录管理

## 技术栈

### 后端
- Django 4.2.7
- Django REST Framework 3.14.0
- SQLite（可配置为PostgreSQL）
- Pillow（图片处理）

### 前端
- Vue.js 3.3.4
- Ant Design Vue 4.0.0
- Vue Router 4.2.4
- Pinia 2.1.6（状态管理）
- Axios（HTTP客户端）
- Vite（构建工具）

## 快速开始

### 1. 安装依赖

#### 后端依赖
```bash
# 创建虚拟环境（推荐）
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装Python依赖
pip install -r requirements.txt
```

#### 前端依赖
```bash
cd frontend
npm install
```

### 2. 数据库设置

```bash
# 创建数据库迁移
python manage.py makemigrations

# 执行迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 3. 启动服务

#### 启动后端服务
```bash
python manage.py runserver
```
后端服务将在 http://localhost:8000 运行

#### 启动前端服务
```bash
cd frontend
npm run dev
```
前端服务将在 http://localhost:3000 运行

### 4. 访问系统

- 前端界面：http://localhost:3000
- 后端管理：http://localhost:8000/admin
- API文档：http://localhost:8000/api

## 项目结构

```
zabbix/
├── asset_management/          # Django项目配置
│   ├── settings.py           # 项目设置
│   ├── urls.py              # 主URL配置
│   └── ...
├── assets/                   # 资产应用
│   ├── models.py            # 数据模型
│   ├── views.py             # API视图
│   ├── serializers.py       # 序列化器
│   ├── admin.py             # 管理后台
│   └── urls.py              # URL配置
├── frontend/                 # Vue.js前端
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── views/           # 页面
│   │   ├── stores/          # 状态管理
│   │   ├── services/        # API服务
│   │   └── router/          # 路由配置
│   ├── package.json
│   └── vite.config.js
├── requirements.txt          # Python依赖
└── README.md                # 项目说明
```

## 数据模型

### 核心模型
- **Asset（资产）**：资产编号、名称、分类、状态、位置、责任人等
- **Category（分类）**：资产分类管理
- **Location（位置）**：资产存放位置
- **MaintenanceRecord（维修记录）**：资产维修保养记录
- **AssetTransfer（转移记录）**：资产位置和责任人变更记录

## API 端点

### 资产相关
- `GET /api/assets/` - 获取资产列表
- `POST /api/assets/` - 创建资产
- `GET /api/assets/{id}/` - 获取资产详情
- `PUT /api/assets/{id}/` - 更新资产
- `DELETE /api/assets/{id}/` - 删除资产
- `GET /api/assets/summary/` - 获取资产统计
- `POST /api/assets/{id}/transfer/` - 资产转移

### 其他端点
- `/api/categories/` - 分类管理
- `/api/locations/` - 位置管理
- `/api/maintenance-records/` - 维修记录
- `/api/asset-transfers/` - 转移记录

## 功能说明

### 资产管理
- 支持资产的完整生命周期管理
- 图片上传和预览
- 状态跟踪（使用中、闲置、维修中、报废）
- 财务信息管理（采购价格、当前价值）

### 搜索和筛选
- 全文搜索支持
- 多条件筛选
- 排序功能
- 分页显示

### 统计报表
- 资产总览仪表板
- 按分类统计
- 按位置统计
- 状态分布图表

### 转移管理
- 位置转移记录
- 责任人变更记录
- 转移历史追踪

## 部署说明

### 生产环境配置

1. **数据库配置**
   - 推荐使用PostgreSQL
   - 更新 `settings.py` 中的数据库配置

2. **静态文件**
   ```bash
   python manage.py collectstatic
   ```

3. **前端构建**
   ```bash
   cd frontend
   npm run build
   ```

4. **环境变量**
   - 设置 `DEBUG=False`
   - 配置 `SECRET_KEY`
   - 设置 `ALLOWED_HOSTS`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
