import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加认证token（如果有的话）
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    const status = error?.response?.status
    if (status === 401) {
      // 未认证，清除本地token并跳转登录
      localStorage.removeItem('token')
      localStorage.removeItem('refresh_token')
      const current = encodeURIComponent(window.location.pathname + window.location.search)
      if (window.location.pathname !== '/login') {
        window.location.href = `/login?redirect=${current}`
      }
    }
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  login: (username, password) => api.post('/auth/token/', { username, password }),
  refresh: (refreshToken) => api.post('/auth/token/refresh/', { refresh: refreshToken })
}

// 资产相关API
export const assetAPI = {
  // 获取资产列表
  getAssets: (params) => api.get('/assets/', { params }),
  
  // 获取资产详情
  getAsset: (id) => api.get(`/assets/${id}/`),
  
  // 创建资产
  createAsset: (data) => api.post('/assets/', data),
  
  // 更新资产
  updateAsset: (id, data) => api.put(`/assets/${id}/`, data),
  
  // 删除资产
  deleteAsset: (id) => api.delete(`/assets/${id}/`),
  
  // 获取资产统计
  getAssetSummary: () => api.get('/assets/summary/'),
  
  // 按分类统计
  getAssetsByCategory: () => api.get('/assets/by_category/'),
  
  // 按位置统计
  getAssetsByLocation: () => api.get('/assets/by_location/'),
  
  // 资产转移
  transferAsset: (id, data) => api.post(`/assets/${id}/transfer/`, data)
}

// 分类相关API
export const categoryAPI = {
  getCategories: (params) => api.get('/categories/', { params }),
  getCategory: (id) => api.get(`/categories/${id}/`),
  createCategory: (data) => api.post('/categories/', data),
  updateCategory: (id, data) => api.put(`/categories/${id}/`, data),
  deleteCategory: (id) => api.delete(`/categories/${id}/`)
}

// 位置相关API
export const locationAPI = {
  getLocations: (params) => api.get('/locations/', { params }),
  getLocation: (id) => api.get(`/locations/${id}/`),
  createLocation: (data) => api.post('/locations/', data),
  updateLocation: (id, data) => api.put(`/locations/${id}/`, data),
  deleteLocation: (id) => api.delete(`/locations/${id}/`)
}

// 维修记录相关API
export const maintenanceAPI = {
  getMaintenanceRecords: (params) => api.get('/maintenance-records/', { params }),
  getMaintenanceRecord: (id) => api.get(`/maintenance-records/${id}/`),
  createMaintenanceRecord: (data) => api.post('/maintenance-records/', data),
  updateMaintenanceRecord: (id, data) => api.put(`/maintenance-records/${id}/`, data),
  deleteMaintenanceRecord: (id) => api.delete(`/maintenance-records/${id}/`)
}

// 转移记录相关API
export const transferAPI = {
  getTransferRecords: (params) => api.get('/asset-transfers/', { params }),
  getTransferRecord: (id) => api.get(`/asset-transfers/${id}/`),
  createTransferRecord: (data) => api.post('/asset-transfers/', data),
  updateTransferRecord: (id, data) => api.put(`/asset-transfers/${id}/`, data),
  deleteTransferRecord: (id) => api.delete(`/asset-transfers/${id}/`)
}

export default api
