import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../layouts/MainLayout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/Login.vue'),
    meta: { public: true, title: '登录' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '仪表板', description: '系统概览和数据统计' }
      },
      // 业务管理模块
      {
        path: '/business',
        name: 'BusinessManagement',
        component: () => import('../views/business/BusinessManagement.vue'),
        meta: { title: '业务管理', description: '管理企业核心业务流程和业务数据' }
      },
      // 资产管理模块
      {
        path: '/assets',
        name: 'AssetManagement',
        redirect: '/assets/list',
        meta: { title: '资产管理', description: '企业资产全生命周期管理' },
        children: [
          {
            path: '/assets/list',
            name: 'AssetList',
            component: () => import('../views/assets/AssetList.vue'),
            meta: { title: '资产列表', description: '查看和管理所有资产' }
          },
          {
            path: '/assets/create',
            name: 'AssetCreate',
            component: () => import('../views/assets/AssetForm.vue'),
            meta: { title: '添加资产', description: '录入新的资产信息' }
          },
          {
            path: '/assets/:id/edit',
            name: 'AssetEdit',
            component: () => import('../views/assets/AssetForm.vue'),
            meta: { title: '编辑资产', description: '修改资产信息' }
          },
          {
            path: '/assets/:id',
            name: 'AssetDetail',
            component: () => import('../views/assets/AssetDetail.vue'),
            meta: { title: '资产详情', description: '查看资产详细信息' }
          },
          {
            path: '/assets/categories',
            name: 'CategoryList',
            component: () => import('../views/categories/CategoryList.vue'),
            meta: { title: '分类管理', description: '管理资产分类和标签' }
          },
          {
            path: '/assets/locations',
            name: 'LocationList',
            component: () => import('../views/locations/LocationList.vue'),
            meta: { title: '位置管理', description: '管理资产存放位置' }
          },
          {
            path: '/assets/maintenance',
            name: 'MaintenanceList',
            component: () => import('../views/maintenance/MaintenanceList.vue'),
            meta: { title: '维修记录', description: '资产维修和保养记录' }
          },
          {
            path: '/assets/transfers',
            name: 'TransferList',
            component: () => import('../views/transfers/TransferList.vue'),
            meta: { title: '转移记录', description: '资产调拨和转移记录' }
          }
        ]
      },
      // 供应链管理模块
      {
        path: '/supply-chain',
        name: 'SupplyChainManagement',
        component: () => import('../views/supply-chain/SupplyChainManagement.vue'),
        meta: { title: '供应链管理', description: '统一管理供应商、采购、库存和物流' }
      },
      // 组织架构管理模块
      {
        path: '/organization',
        name: 'OrganizationManagement',
        component: () => import('../views/organization/OrganizationManagement.vue'),
        meta: { title: '组织架构管理', description: '管理企业组织结构和人员配置' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫：需要登录的页面检查本地token
router.beforeEach((to, from, next) => {
  if (to.meta.public) return next()
  const token = localStorage.getItem('token')
  if (!token) {
    const redirect = encodeURIComponent(to.fullPath)
    return next({ path: '/login', query: { redirect } })
  }
  next()
})

export default router
