import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../layouts/MainLayout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/Login.vue'),
    meta: { public: true, title: '登录' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '仪表板' }
      },
      {
        path: '/assets',
        name: 'AssetList',
        component: () => import('../views/assets/AssetList.vue'),
        meta: { title: '资产列表' }
      },
      {
        path: '/assets/create',
        name: 'AssetCreate',
        component: () => import('../views/assets/AssetForm.vue'),
        meta: { title: '添加资产' }
      },
      {
        path: '/assets/:id/edit',
        name: 'AssetEdit',
        component: () => import('../views/assets/AssetForm.vue'),
        meta: { title: '编辑资产' }
      },
      {
        path: '/assets/:id',
        name: 'AssetDetail',
        component: () => import('../views/assets/AssetDetail.vue'),
        meta: { title: '资产详情' }
      },
      {
        path: '/categories',
        name: 'CategoryList',
        component: () => import('../views/categories/CategoryList.vue'),
        meta: { title: '分类管理' }
      },
      {
        path: '/locations',
        name: 'LocationList',
        component: () => import('../views/locations/LocationList.vue'),
        meta: { title: '位置管理' }
      },
      {
        path: '/maintenance',
        name: 'MaintenanceList',
        component: () => import('../views/maintenance/MaintenanceList.vue'),
        meta: { title: '维修记录' }
      },
      {
        path: '/transfers',
        name: 'TransferList',
        component: () => import('../views/transfers/TransferList.vue'),
        meta: { title: '转移记录' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫：需要登录的页面检查本地token
router.beforeEach((to, from, next) => {
  if (to.meta.public) return next()
  const token = localStorage.getItem('token')
  if (!token) {
    const redirect = encodeURIComponent(to.fullPath)
    return next({ path: '/login', query: { redirect } })
  }
  next()
})

export default router
