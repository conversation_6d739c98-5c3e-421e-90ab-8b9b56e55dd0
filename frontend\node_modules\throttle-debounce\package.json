{"name": "throttle-debounce", "version": "5.0.2", "description": "Throttle and debounce functions.", "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://ivannikolic.com)", "contributors": ["<PERSON> (http://benalman.com)"], "sideEffects": false, "exports": {".": {"import": "./esm/index.js", "require": "./cjs/index.js"}, "./package.json": "./package.json"}, "main": "cjs/index.js", "jsdelivr": "umd/index.js", "unpkg": "umd/index.js", "module": "esm/index.js", "directories": {"test": "test"}, "files": ["cjs/", "esm/", "umd/", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"build": "rollup --config rollup.config.js", "lint": "eslint '{index,debounce,throttle,test/**/*}.js'", "module-check": "node -e 'require(\"throttle-debounce\");' && node --input-type=module -e 'import \"throttle-debounce\";'", "prepublishOnly": "npm run build", "postpublish": "GITHUB_TOKEN=$GITHUB_RELEASE_TOKEN github-release-from-changelog", "release": "np --no-release-draft", "test": "npm run test:automated", "test:automated": "BABEL_ENV=test karma start", "test:automated:watch": "npm run test:automated -- --auto-watch --no-single-run", "version": "if [ $(git rev-parse --abbrev-ref HEAD) == 'master' ]; then sed -i '' '/\\[unreleased\\]:/d' CHANGELOG.md && version-changelog CHANGELOG.md && changelog-verify CHANGELOG.md && git add CHANGELOG.md; else echo; fi", "prerelease": "npm run lint && npm run build && npm run module-check"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.12.1", "@babel/runtime": "^7.2.0", "@rollup/plugin-babel": "^5.2.1", "babel-loader": "^8.1.0", "changelog-verify": "^1.1.2", "core-js": "^2.6.5", "eslint": "^8.5.0", "eslint-config-nitpick": "^11.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "esm": "^3.0.51", "get-port": "^4.0.0", "get-port-cli": "^2.0.0", "github-release-from-changelog": "^2.1.1", "husky": "^4.3.0", "karma": "^5.2.3", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^0.1.7", "karma-mocha-reporter": "^2.2.5", "karma-qunit": "^0.1.9", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "5.0.0", "lint-staged": "^10.4.2", "minimist": "^1.2.0", "node-stdlib-browser": "^1.2.0", "np": "^7.6.0", "prettier": "^2.4.0", "qunitjs": "^1.23.1", "rollup": "^2.32.1", "version-changelog": "^3.1.1", "webpack": "^5.2.0"}, "engines": {"node": ">=12.22"}, "keywords": ["debounce", "throttle"], "repository": {"type": "git", "url": "git+https://github.com/niksy/throttle-debounce.git"}, "bugs": {"url": "https://github.com/niksy/throttle-debounce/issues"}, "homepage": "https://github.com/niksy/throttle-debounce#readme"}