function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function t(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}function n(){return(n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function r(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function i(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)t.indexOf(n=o[r])>=0||(i[n]=e[n]);return i}function o(e){return 1==(null!=(t=e)&&"object"==typeof t&&!1===Array.isArray(t))&&"[object Object]"===Object.prototype.toString.call(e);var t}var u=Object.prototype,a=u.toString,f=u.hasOwnProperty,c=/^\s*function (\w+)/;function l(e){var t,n=null!==(t=null==e?void 0:e.type)&&void 0!==t?t:e;if(n){var r=n.toString().match(c);return r?r[1]:""}return""}var s=function(e){var t,n;return!1!==o(e)&&"function"==typeof(t=e.constructor)&&!1!==o(n=t.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")},v=function(e){return e},y=v;if("production"!==process.env.NODE_ENV){var p="undefined"!=typeof console;y=p?function(e){console.warn("[VueTypes warn]: "+e)}:v}var d=function(e,t){return f.call(e,t)},h=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},b=Array.isArray||function(e){return"[object Array]"===a.call(e)},O=function(e){return"[object Function]"===a.call(e)},g=function(e){return s(e)&&d(e,"_vueTypes_name")},m=function(e){return s(e)&&(d(e,"type")||["_vueTypes_name","validator","default","required"].some(function(t){return d(e,t)}))};function j(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function _(e,t,n){var r;void 0===n&&(n=!1);var i=!0,o="";r=s(e)?e:{type:e};var u=g(r)?r._vueTypes_name+" - ":"";if(m(r)&&null!==r.type){if(void 0===r.type||!0===r.type)return i;if(!r.required&&void 0===t)return i;b(r.type)?(i=r.type.some(function(e){return!0===_(e,t,!0)}),o=r.type.map(function(e){return l(e)}).join(" or ")):i="Array"===(o=l(r))?b(t):"Object"===o?s(t):"String"===o||"Number"===o||"Boolean"===o||"Function"===o?function(e){if(null==e)return"";var t=e.constructor.toString().match(c);return t?t[1]:""}(t)===o:t instanceof r.type}if(!i){var a=u+'value "'+t+'" should be of type "'+o+'"';return!1===n?(y(a),!1):a}if(d(r,"validator")&&O(r.validator)){var f=y,v=[];if(y=function(e){v.push(e)},i=r.validator(t),y=f,!i){var p=(v.length>1?"* ":"")+v.join("\n* ");return v.length=0,!1===n?(y(p),i):p}}return i}function T(e,t){var n=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get:function(){return this.required=!0,this}},def:{value:function(e){return void 0!==e||this.default?O(e)||!0===_(this,e,!0)?(this.default=b(e)?function(){return[].concat(e)}:s(e)?function(){return Object.assign({},e)}:e,this):(y(this._vueTypes_name+' - invalid default value: "'+e+'"'),this):this}}}),r=n.validator;return O(r)&&(n.validator=j(r,n)),n}function w(e,t){var n=T(e,t);return Object.defineProperty(n,"validate",{value:function(e){return O(this.validator)&&y(this._vueTypes_name+" - calling .validate() will overwrite the current custom validator function. Validator info:\n"+JSON.stringify(this)),this.validator=j(e,this),this}})}function k(e,t,n){var r,o,u=(r=t,o={},Object.getOwnPropertyNames(r).forEach(function(e){o[e]=Object.getOwnPropertyDescriptor(r,e)}),Object.defineProperties({},o));if(u._vueTypes_name=e,!s(n))return u;var a,f,c=n.validator,l=i(n,["validator"]);if(O(c)){var v=u.validator;v&&(v=null!==(f=(a=v).__original)&&void 0!==f?f:a),u.validator=j(v?function(e){return v.call(this,e)&&c.call(this,e)}:c,u)}return Object.assign(u,l)}function P(e){return e.replace(/^(?!\s*$)/gm,"  ")}var x=function(){return w("any",{})},A=function(){return w("function",{type:Function})},E=function(){return w("boolean",{type:Boolean})},N=function(){return w("string",{type:String})},q=function(){return w("number",{type:Number})},S=function(){return w("array",{type:Array})},V=function(){return w("object",{type:Object})},F=function(){return T("integer",{type:Number,validator:function(e){return h(e)}})},D=function(){return T("symbol",{validator:function(e){return"symbol"==typeof e}})};function L(e,t){if(void 0===t&&(t="custom validation failed"),"function"!=typeof e)throw new TypeError("[VueTypes error]: You must provide a function as argument");return T(e.name||"<<anonymous function>>",{validator:function(n){var r=e(n);return r||y(this._vueTypes_name+" - "+t),r}})}function Y(e){if(!b(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");var t='oneOf - value should be one of "'+e.join('", "')+'".',n=e.reduce(function(e,t){if(null!=t){var n=t.constructor;-1===e.indexOf(n)&&e.push(n)}return e},[]);return T("oneOf",{type:n.length>0?n:void 0,validator:function(n){var r=-1!==e.indexOf(n);return r||y(t),r}})}function B(e){if(!b(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");for(var t=!1,n=[],r=0;r<e.length;r+=1){var i=e[r];if(m(i)){if(g(i)&&"oneOf"===i._vueTypes_name){n=n.concat(i.type);continue}if(O(i.validator)&&(t=!0),!0!==i.type&&i.type){n=n.concat(i.type);continue}}n.push(i)}return n=n.filter(function(e,t){return n.indexOf(e)===t}),T("oneOfType",t?{type:n,validator:function(t){var n=[],r=e.some(function(e){var r=_(g(e)&&"oneOf"===e._vueTypes_name?e.type||null:e,t,!0);return"string"==typeof r&&n.push(r),!0===r});return r||y("oneOfType - provided value does not match any of the "+n.length+" passed-in validators:\n"+P(n.join("\n"))),r}}:{type:n})}function I(e){return T("arrayOf",{type:Array,validator:function(t){var n,r=t.every(function(t){return!0===(n=_(e,t,!0))});return r||y("arrayOf - value validation error:\n"+P(n)),r}})}function J(e){return T("instanceOf",{type:e})}function M(e){return T("objectOf",{type:Object,validator:function(t){var n,r=Object.keys(t).every(function(r){return!0===(n=_(e,t[r],!0))});return r||y("objectOf - value validation error:\n"+P(n)),r}})}function R(e){var t=Object.keys(e),n=t.filter(function(t){var n;return!!(null===(n=e[t])||void 0===n?void 0:n.required)}),r=T("shape",{type:Object,validator:function(r){var i=this;if(!s(r))return!1;var o=Object.keys(r);if(n.length>0&&n.some(function(e){return-1===o.indexOf(e)})){var u=n.filter(function(e){return-1===o.indexOf(e)});return y(1===u.length?'shape - required property "'+u[0]+'" is not defined.':'shape - required properties "'+u.join('", "')+'" are not defined.'),!1}return o.every(function(n){if(-1===t.indexOf(n))return!0===i._vueTypes_isLoose||(y('shape - shape definition does not include a "'+n+'" property. Allowed keys: "'+t.join('", "')+'".'),!1);var o=_(e[n],r[n],!0);return"string"==typeof o&&y('shape - "'+n+'" property validation error:\n '+P(o)),!0===o})}});return Object.defineProperty(r,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(r,"loose",{get:function(){return this._vueTypes_isLoose=!0,this}}),r}var $=function(){function e(){}return e.extend=function(e){var t=this;if(b(e))return e.forEach(function(e){return t.extend(e)}),this;var n=e.name,r=e.validate,o=void 0!==r&&r,u=e.getter,a=void 0!==u&&u,f=i(e,["name","validate","getter"]);if(d(this,n))throw new TypeError('[VueTypes error]: Type "'+n+'" already defined');var c,l=f.type;return g(l)?(delete f.type,Object.defineProperty(this,n,a?{get:function(){return k(n,l,f)}}:{value:function(){var e,t=k(n,l,f);return t.validator&&(t.validator=(e=t.validator).bind.apply(e,[t].concat([].slice.call(arguments)))),t}})):(c=a?{get:function(){var e=Object.assign({},f);return o?w(n,e):T(n,e)},enumerable:!0}:{value:function(){var e,t,r=Object.assign({},f);return e=o?w(n,r):T(n,r),r.validator&&(e.validator=(t=r.validator).bind.apply(t,[e].concat([].slice.call(arguments)))),e},enumerable:!0},Object.defineProperty(this,n,c))},t(e,null,[{key:"any",get:function(){return x()}},{key:"func",get:function(){return A().def(this.defaults.func)}},{key:"bool",get:function(){return E().def(this.defaults.bool)}},{key:"string",get:function(){return N().def(this.defaults.string)}},{key:"number",get:function(){return q().def(this.defaults.number)}},{key:"array",get:function(){return S().def(this.defaults.array)}},{key:"object",get:function(){return V().def(this.defaults.object)}},{key:"integer",get:function(){return F().def(this.defaults.integer)}},{key:"symbol",get:function(){return D()}}]),e}();function z(e){var i;return void 0===e&&(e={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(i=function(i){function o(){return i.apply(this,arguments)||this}return r(o,i),t(o,null,[{key:"sensibleDefaults",get:function(){return n({},this.defaults)},set:function(t){this.defaults=!1!==t?n({},!0!==t?t:e):{}}}]),o}($)).defaults=n({},e),i}$.defaults={},$.custom=L,$.oneOf=Y,$.instanceOf=J,$.oneOfType=B,$.arrayOf=I,$.objectOf=M,$.shape=R,$.utils={validate:function(e,t){return!0===_(t,e,!0)},toType:function(e,t,n){return void 0===n&&(n=!1),n?w(e,t):T(e,t)}};var C=function(e){function t(){return e.apply(this,arguments)||this}return r(t,e),t}(z());export default C;export{x as any,S as array,I as arrayOf,E as bool,z as createTypes,L as custom,k as fromType,A as func,J as instanceOf,F as integer,q as number,V as object,M as objectOf,Y as oneOf,B as oneOfType,R as shape,N as string,D as symbol,T as toType,w as toValidableType,_ as validateType};
//# sourceMappingURL=vue-types.m.js.map
