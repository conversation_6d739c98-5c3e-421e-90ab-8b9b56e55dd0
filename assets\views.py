from django.shortcuts import render
from django.db.models import Count, Sum, Q
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from .models import Category, Location, Asset, MaintenanceRecord, AssetTransfer
from .serializers import (
    CategorySerializer, LocationSerializer, AssetSerializer,
    MaintenanceRecordSerializer, AssetTransferSerializer, AssetSummarySerializer
)


class CategoryViewSet(viewsets.ModelViewSet):
    """资产分类视图集"""
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class LocationViewSet(viewsets.ModelViewSet):
    """位置视图集"""
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'address', 'contact_person']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class AssetViewSet(viewsets.ModelViewSet):
    """资产视图集"""
    queryset = Asset.objects.select_related('category', 'location', 'responsible_person').all()
    serializer_class = AssetSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'category', 'location', 'responsible_person']
    search_fields = ['asset_number', 'name', 'brand', 'model', 'serial_number']
    ordering_fields = ['asset_number', 'name', 'purchase_date', 'created_at']
    ordering = ['-created_at']

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """获取资产统计摘要"""
        total_assets = Asset.objects.count()
        active_assets = Asset.objects.filter(status='active').count()
        inactive_assets = Asset.objects.filter(status='inactive').count()
        maintenance_assets = Asset.objects.filter(status='maintenance').count()
        scrapped_assets = Asset.objects.filter(status='scrapped').count()
        
        total_value = Asset.objects.aggregate(
            total=Sum('current_value')
        )['total'] or 0
        
        categories_count = Category.objects.count()
        locations_count = Location.objects.count()
        
        summary_data = {
            'total_assets': total_assets,
            'active_assets': active_assets,
            'inactive_assets': inactive_assets,
            'maintenance_assets': maintenance_assets,
            'scrapped_assets': scrapped_assets,
            'total_value': total_value,
            'categories_count': categories_count,
            'locations_count': locations_count,
        }
        
        serializer = AssetSummarySerializer(summary_data)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """按分类统计资产"""
        stats = Category.objects.annotate(
            asset_count=Count('asset'),
            active_count=Count('asset', filter=Q(asset__status='active')),
            total_value=Sum('asset__current_value')
        ).values('id', 'name', 'asset_count', 'active_count', 'total_value')
        
        return Response(list(stats))

    @action(detail=False, methods=['get'])
    def by_location(self, request):
        """按位置统计资产"""
        stats = Location.objects.annotate(
            asset_count=Count('asset'),
            active_count=Count('asset', filter=Q(asset__status='active')),
            total_value=Sum('asset__current_value')
        ).values('id', 'name', 'asset_count', 'active_count', 'total_value')
        
        return Response(list(stats))

    @action(detail=True, methods=['post'])
    def transfer(self, request, pk=None):
        """资产转移"""
        asset = self.get_object()
        data = request.data.copy()
        data['asset'] = asset.id
        data['created_by'] = request.user.id
        
        # 记录原位置和责任人
        data['from_location'] = asset.location.id if asset.location else None
        data['from_person'] = asset.responsible_person.id if asset.responsible_person else None
        
        serializer = AssetTransferSerializer(data=data)
        if serializer.is_valid():
            transfer_record = serializer.save()
            
            # 更新资产的位置和责任人
            if 'to_location' in data and data['to_location']:
                asset.location_id = data['to_location']
            if 'to_person' in data and data['to_person']:
                asset.responsible_person_id = data['to_person']
            asset.save()
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MaintenanceRecordViewSet(viewsets.ModelViewSet):
    """维修记录视图集"""
    queryset = MaintenanceRecord.objects.select_related('asset', 'created_by').all()
    serializer_class = MaintenanceRecordSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['asset', 'maintenance_type', 'maintenance_date']
    search_fields = ['asset__name', 'asset__asset_number', 'description', 'technician']
    ordering_fields = ['maintenance_date', 'cost', 'created_at']
    ordering = ['-maintenance_date']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class AssetTransferViewSet(viewsets.ModelViewSet):
    """资产转移记录视图集"""
    queryset = AssetTransfer.objects.select_related(
        'asset', 'from_location', 'to_location', 'from_person', 'to_person', 'created_by'
    ).all()
    serializer_class = AssetTransferSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['asset', 'from_location', 'to_location', 'transfer_date']
    search_fields = ['asset__name', 'asset__asset_number', 'reason']
    ordering_fields = ['transfer_date', 'created_at']
    ordering = ['-transfer_date']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
