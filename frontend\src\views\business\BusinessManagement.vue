<template>
  <div class="business-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">业务管理</h1>
          <p class="page-subtitle">管理企业核心业务流程和业务数据</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <PlusOutlined />
            </template>
            新建业务
          </a-button>
        </div>
      </div>
    </div>

    <!-- 业务概览卡片 -->
    <div class="overview-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="(card, index) in overviewCards" :key="index">
          <div class="overview-card" :class="`card-${index + 1}`">
            <div class="card-icon">
              <component :is="card.icon" />
            </div>
            <div class="card-content">
              <div class="card-value">{{ card.value }}</div>
              <div class="card-title">{{ card.title }}</div>
              <div class="card-trend" :class="card.trend">
                <component :is="card.trendIcon" />
                <span>{{ card.trendText }}</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 业务模块 -->
    <div class="modules-section">
      <div class="section-header">
        <h2>业务模块</h2>
        <a-input-search
          placeholder="搜索业务模块..."
          style="width: 300px"
          @search="handleSearch"
        />
      </div>
      
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="8" v-for="module in businessModules" :key="module.id">
          <div class="module-card" @click="handleModuleClick(module)">
            <div class="module-header">
              <div class="module-icon">
                <component :is="module.icon" />
              </div>
              <div class="module-status" :class="module.status">
                {{ module.statusText }}
              </div>
            </div>
            <div class="module-content">
              <h3 class="module-title">{{ module.title }}</h3>
              <p class="module-description">{{ module.description }}</p>
              <div class="module-stats">
                <div class="stat-item">
                  <span class="stat-label">活跃用户</span>
                  <span class="stat-value">{{ module.activeUsers }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">今日处理</span>
                  <span class="stat-value">{{ module.todayProcessed }}</span>
                </div>
              </div>
            </div>
            <div class="module-actions">
              <a-button type="text" size="small" @click.stop="handleModuleConfig(module)">
                <SettingOutlined />
                配置
              </a-button>
              <a-button type="text" size="small" @click.stop="handleModuleReport(module)">
                <BarChartOutlined />
                报表
              </a-button>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <div class="activity-card">
        <div class="activity-header">
          <h3>最近活动</h3>
          <a-button type="text" size="small">
            查看全部
          </a-button>
        </div>
        <div class="activity-list">
          <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
            <div class="activity-avatar">
              <a-avatar :style="{ backgroundColor: activity.color }">
                <component :is="activity.icon" />
              </a-avatar>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-description">{{ activity.description }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
            <div class="activity-status">
              <a-tag :color="activity.statusColor">{{ activity.status }}</a-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建业务模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="新建业务模块"
      @ok="handleCreateBusiness"
      @cancel="showCreateModal = false"
    >
      <a-form :model="createForm" layout="vertical">
        <a-form-item label="模块名称" required>
          <a-input v-model:value="createForm.name" placeholder="请输入模块名称" />
        </a-form-item>
        <a-form-item label="模块描述">
          <a-textarea v-model:value="createForm.description" placeholder="请输入模块描述" :rows="3" />
        </a-form-item>
        <a-form-item label="模块类型" required>
          <a-select v-model:value="createForm.type" placeholder="请选择模块类型">
            <a-select-option value="sales">销售管理</a-select-option>
            <a-select-option value="purchase">采购管理</a-select-option>
            <a-select-option value="inventory">库存管理</a-select-option>
            <a-select-option value="finance">财务管理</a-select-option>
            <a-select-option value="hr">人力资源</a-select-option>
            <a-select-option value="project">项目管理</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  SettingOutlined,
  BarChartOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  TeamOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const showCreateModal = ref(false)

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  type: ''
})

// 概览卡片数据
const overviewCards = ref([
  {
    title: '活跃业务模块',
    value: '12',
    icon: ShopOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+2 较上月'
  },
  {
    title: '今日处理量',
    value: '1,234',
    icon: ShoppingCartOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+15% 较昨日'
  },
  {
    title: '业务收入',
    value: '¥2.5M',
    icon: DollarOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+8% 较上月'
  },
  {
    title: '参与用户',
    value: '856',
    icon: TeamOutlined,
    trend: 'down',
    trendIcon: ArrowDownOutlined,
    trendText: '-3% 较上月'
  }
])

// 业务模块数据
const businessModules = ref([
  {
    id: 1,
    title: '销售管理',
    description: '管理销售流程、客户关系和销售业绩',
    icon: ShopOutlined,
    status: 'active',
    statusText: '运行中',
    activeUsers: 45,
    todayProcessed: 128
  },
  {
    id: 2,
    title: '采购管理',
    description: '供应商管理、采购流程和成本控制',
    icon: ShoppingCartOutlined,
    status: 'active',
    statusText: '运行中',
    activeUsers: 32,
    todayProcessed: 89
  },
  {
    id: 3,
    title: '库存管理',
    description: '库存监控、出入库管理和库存优化',
    icon: BarChartOutlined,
    status: 'warning',
    statusText: '需关注',
    activeUsers: 28,
    todayProcessed: 156
  },
  {
    id: 4,
    title: '财务管理',
    description: '财务核算、预算管理和财务分析',
    icon: DollarOutlined,
    status: 'active',
    statusText: '运行中',
    activeUsers: 18,
    todayProcessed: 67
  },
  {
    id: 5,
    title: '人力资源',
    description: '员工管理、薪酬福利和绩效考核',
    icon: TeamOutlined,
    status: 'maintenance',
    statusText: '维护中',
    activeUsers: 0,
    todayProcessed: 0
  },
  {
    id: 6,
    title: '项目管理',
    description: '项目规划、进度跟踪和资源分配',
    icon: SettingOutlined,
    status: 'active',
    statusText: '运行中',
    activeUsers: 24,
    todayProcessed: 43
  }
])

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '销售订单创建',
    description: '张三创建了新的销售订单 #SO2024001',
    time: '2分钟前',
    icon: ShopOutlined,
    color: '#1890ff',
    status: '已完成',
    statusColor: 'green'
  },
  {
    id: 2,
    title: '采购申请审批',
    description: '李四提交的采购申请等待审批',
    time: '5分钟前',
    icon: ShoppingCartOutlined,
    color: '#52c41a',
    status: '待审批',
    statusColor: 'orange'
  },
  {
    id: 3,
    title: '库存预警',
    description: '产品A库存不足，当前库存：5件',
    time: '10分钟前',
    icon: ExclamationCircleOutlined,
    color: '#faad14',
    status: '需处理',
    statusColor: 'red'
  },
  {
    id: 4,
    title: '财务报表生成',
    description: '系统自动生成了月度财务报表',
    time: '15分钟前',
    icon: BarChartOutlined,
    color: '#722ed1',
    status: '已完成',
    statusColor: 'green'
  }
])

// 事件处理
const handleSearch = (value) => {
  console.log('搜索:', value)
}

const handleModuleClick = (module) => {
  router.push(`/business/${module.id}`)
}

const handleModuleConfig = (module) => {
  console.log('配置模块:', module)
}

const handleModuleReport = (module) => {
  console.log('查看报表:', module)
}

const handleCreateBusiness = () => {
  console.log('创建业务:', createForm)
  showCreateModal.value = false
}
</script>

<style scoped>
/* 业务管理页面特有样式 */
.business-management {
  padding: 0;
}

/* 业务模块区域 */
.modules-section {
  margin-bottom: 24px;
}

/* 活动列表样式 */
.activity-section {
  margin-bottom: 24px;
}

.activity-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.activity-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.activity-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.activity-list {
  padding: 24px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: rgba(24, 144, 255, 0.02);
  border-radius: 8px;
  margin: 0 -8px;
  padding: 16px 8px;
}

.activity-avatar {
  position: relative;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.activity-status {
  flex-shrink: 0;
}

/* 统计行样式 */
.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row .stat-label {
  font-size: 14px;
  color: #666;
}

.stat-row .stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .activity-avatar {
    align-self: center;
  }

  .activity-status {
    align-self: flex-end;
  }
}
</style>
