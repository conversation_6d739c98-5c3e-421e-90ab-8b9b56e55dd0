<template>
  <div class="login-container">
    <a-card class="login-card" :title="'登录资产管理系统'">
      <a-form :model="form" @submit.prevent="onSubmit" layout="vertical">
        <a-form-item label="用户名" required>
          <a-input v-model:value="form.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="密码" required>
          <a-input-password v-model:value="form.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" block :loading="loading" @click="onSubmit">登录</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { authAPI } from '../../services/api'

const router = useRouter()
const route = useRoute()
const loading = ref(false)

const form = reactive({
  username: '',
  password: ''
})

const onSubmit = async () => {
  if (!form.username || !form.password) {
    message.warning('请输入用户名和密码')
    return
  }
  loading.value = true
  try {
    const res = await authAPI.login(form.username, form.password)
    // SimpleJWT returns { access, refresh }
    const access = res.access
    const refresh = res.refresh
    if (access) localStorage.setItem('token', access)
    if (refresh) localStorage.setItem('refresh_token', refresh)
    message.success('登录成功')
    const redirect = route.query.redirect || '/'
    router.replace(String(redirect))
  } catch (e) {
    message.error('登录失败，请检查用户名或密码')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}
.login-card {
  width: 360px;
}
</style>
