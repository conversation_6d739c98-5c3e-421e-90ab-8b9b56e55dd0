<template>
  <div class="organization-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">组织架构管理</h1>
          <p class="page-subtitle">管理企业组织结构、部门设置和人员配置</p>
        </div>
        <div class="header-actions">
          <a-button @click="showDepartmentModal = true">
            <template #icon>
              <PlusOutlined />
            </template>
            新建部门
          </a-button>
          <a-button type="primary" @click="showEmployeeModal = true">
            <template #icon>
              <UserAddOutlined />
            </template>
            添加员工
          </a-button>
        </div>
      </div>
    </div>

    <!-- 组织概览 -->
    <div class="overview-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="(card, index) in overviewCards" :key="index">
          <div class="overview-card" :class="`card-${index + 1}`">
            <div class="card-icon">
              <component :is="card.icon" />
            </div>
            <div class="card-content">
              <div class="card-value">{{ card.value }}</div>
              <div class="card-title">{{ card.title }}</div>
              <div class="card-trend" :class="card.trend">
                <component :is="card.trendIcon" />
                <span>{{ card.trendText }}</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 组织架构图 -->
    <div class="org-chart-section">
      <div class="section-header">
        <h2>组织架构图</h2>
        <div class="header-controls">
          <a-button @click="expandAll">
            <ExpandOutlined />
            展开全部
          </a-button>
          <a-button @click="collapseAll">
            <ShrinkOutlined />
            收起全部
          </a-button>
          <a-button @click="refreshOrgChart">
            <ReloadOutlined />
            刷新
          </a-button>
        </div>
      </div>
      
      <div class="org-chart">
        <div class="org-node root-node">
          <div class="node-content">
            <div class="node-avatar">
              <a-avatar :size="48" style="background-color: #1890ff">
                <CrownOutlined />
              </a-avatar>
            </div>
            <div class="node-info">
              <h4 class="node-title">董事会</h4>
              <p class="node-subtitle">Board of Directors</p>
              <span class="node-count">3人</span>
            </div>
          </div>
          
          <div class="org-children">
            <div class="org-node" v-for="department in departments" :key="department.id">
              <div class="node-content" @click="handleDepartmentClick(department)">
                <div class="node-avatar">
                  <a-avatar :size="40" :style="{ backgroundColor: department.color }">
                    <component :is="department.icon" />
                  </a-avatar>
                </div>
                <div class="node-info">
                  <h4 class="node-title">{{ department.name }}</h4>
                  <p class="node-subtitle">{{ department.description }}</p>
                  <span class="node-count">{{ department.employeeCount }}人</span>
                </div>
                <div class="node-actions">
                  <a-dropdown>
                    <a-button type="text" size="small">
                      <MoreOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handleDepartmentAction">
                        <a-menu-item key="edit" :data-id="department.id">编辑部门</a-menu-item>
                        <a-menu-item key="add-employee" :data-id="department.id">添加员工</a-menu-item>
                        <a-menu-item key="view-detail" :data-id="department.id">查看详情</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="delete" :data-id="department.id" danger>删除部门</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
              
              <!-- 子部门 -->
              <div class="org-children" v-if="department.children && department.children.length > 0">
                <div class="org-node sub-node" v-for="subDept in department.children" :key="subDept.id">
                  <div class="node-content" @click="handleDepartmentClick(subDept)">
                    <div class="node-avatar">
                      <a-avatar :size="32" :style="{ backgroundColor: subDept.color }">
                        <component :is="subDept.icon" />
                      </a-avatar>
                    </div>
                    <div class="node-info">
                      <h5 class="node-title">{{ subDept.name }}</h5>
                      <span class="node-count">{{ subDept.employeeCount }}人</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 部门详情 -->
    <div class="department-detail-section" v-if="selectedDepartment">
      <div class="detail-card">
        <div class="detail-header">
          <div class="dept-info">
            <a-avatar :size="64" :style="{ backgroundColor: selectedDepartment.color }">
              <component :is="selectedDepartment.icon" />
            </a-avatar>
            <div class="dept-text">
              <h3>{{ selectedDepartment.name }}</h3>
              <p>{{ selectedDepartment.description }}</p>
              <div class="dept-stats">
                <span class="stat-item">员工数量: {{ selectedDepartment.employeeCount }}</span>
                <span class="stat-item">负责人: {{ selectedDepartment.manager }}</span>
                <span class="stat-item">成立时间: {{ selectedDepartment.establishDate }}</span>
              </div>
            </div>
          </div>
          <div class="detail-actions">
            <a-button @click="handleAddEmployee">
              <UserAddOutlined />
              添加员工
            </a-button>
            <a-button @click="handleEditDepartment">
              <EditOutlined />
              编辑部门
            </a-button>
          </div>
        </div>
        
        <!-- 员工列表 -->
        <div class="employees-section">
          <div class="employees-header">
            <h4>部门员工</h4>
            <a-input-search
              placeholder="搜索员工..."
              style="width: 250px"
              @search="handleEmployeeSearch"
            />
          </div>
          
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="8" v-for="employee in selectedDepartment.employees" :key="employee.id">
              <div class="employee-card">
                <div class="employee-avatar">
                  <a-avatar :size="48" :src="employee.avatar">
                    {{ employee.name.charAt(0) }}
                  </a-avatar>
                  <div class="employee-status" :class="employee.status"></div>
                </div>
                <div class="employee-info">
                  <h5 class="employee-name">{{ employee.name }}</h5>
                  <p class="employee-position">{{ employee.position }}</p>
                  <p class="employee-email">{{ employee.email }}</p>
                  <div class="employee-tags">
                    <a-tag v-for="tag in employee.tags" :key="tag" size="small">{{ tag }}</a-tag>
                  </div>
                </div>
                <div class="employee-actions">
                  <a-dropdown>
                    <a-button type="text" size="small">
                      <MoreOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handleEmployeeAction">
                        <a-menu-item key="profile" :data-id="employee.id">查看档案</a-menu-item>
                        <a-menu-item key="edit" :data-id="employee.id">编辑信息</a-menu-item>
                        <a-menu-item key="transfer" :data-id="employee.id">调动部门</a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="disable" :data-id="employee.id" danger>停用账号</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>

    <!-- 新建部门模态框 -->
    <a-modal
      v-model:open="showDepartmentModal"
      title="新建部门"
      @ok="handleCreateDepartment"
      @cancel="showDepartmentModal = false"
    >
      <a-form :model="departmentForm" layout="vertical">
        <a-form-item label="部门名称" required>
          <a-input v-model:value="departmentForm.name" placeholder="请输入部门名称" />
        </a-form-item>
        <a-form-item label="上级部门">
          <a-tree-select
            v-model:value="departmentForm.parentId"
            :tree-data="departmentTreeData"
            placeholder="请选择上级部门"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="部门描述">
          <a-textarea v-model:value="departmentForm.description" placeholder="请输入部门描述" :rows="3" />
        </a-form-item>
        <a-form-item label="部门负责人">
          <a-select v-model:value="departmentForm.managerId" placeholder="请选择负责人">
            <a-select-option v-for="emp in allEmployees" :key="emp.id" :value="emp.id">
              {{ emp.name }} - {{ emp.position }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加员工模态框 -->
    <a-modal
      v-model:open="showEmployeeModal"
      title="添加员工"
      width="600px"
      @ok="handleCreateEmployee"
      @cancel="showEmployeeModal = false"
    >
      <a-form :model="employeeForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="姓名" required>
              <a-input v-model:value="employeeForm.name" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工号" required>
              <a-input v-model:value="employeeForm.employeeId" placeholder="请输入工号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所属部门" required>
              <a-tree-select
                v-model:value="employeeForm.departmentId"
                :tree-data="departmentTreeData"
                placeholder="请选择部门"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="职位" required>
              <a-input v-model:value="employeeForm.position" placeholder="请输入职位" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="邮箱">
              <a-input v-model:value="employeeForm.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号">
              <a-input v-model:value="employeeForm.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="入职日期">
          <a-date-picker v-model:value="employeeForm.joinDate" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  UserAddOutlined,
  ExpandOutlined,
  ShrinkOutlined,
  ReloadOutlined,
  MoreOutlined,
  EditOutlined,
  CrownOutlined,
  TeamOutlined,
  ShopOutlined,
  ToolOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const showDepartmentModal = ref(false)
const showEmployeeModal = ref(false)
const selectedDepartment = ref(null)

// 表单数据
const departmentForm = reactive({
  name: '',
  parentId: null,
  description: '',
  managerId: null
})

const employeeForm = reactive({
  name: '',
  employeeId: '',
  departmentId: null,
  position: '',
  email: '',
  phone: '',
  joinDate: null
})

// 概览卡片数据
const overviewCards = ref([
  {
    title: '总员工数',
    value: '1,234',
    icon: TeamOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+45 较上月'
  },
  {
    title: '部门数量',
    value: '28',
    icon: ShopOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+2 较上月'
  },
  {
    title: '在职率',
    value: '96.8%',
    icon: ToolOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '****% 较上月'
  },
  {
    title: '平均薪资',
    value: '¥12.5K',
    icon: DollarOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+8% 较上月'
  }
])

// 部门数据
const departments = ref([
  {
    id: 1,
    name: '技术部',
    description: '负责产品研发和技术创新',
    icon: ToolOutlined,
    color: '#1890ff',
    employeeCount: 45,
    manager: '张三',
    establishDate: '2020-01-15',
    children: [
      {
        id: 11,
        name: '前端组',
        icon: ToolOutlined,
        color: '#40a9ff',
        employeeCount: 15
      },
      {
        id: 12,
        name: '后端组',
        icon: ToolOutlined,
        color: '#1890ff',
        employeeCount: 20
      },
      {
        id: 13,
        name: '测试组',
        icon: ToolOutlined,
        color: '#096dd9',
        employeeCount: 10
      }
    ],
    employees: [
      {
        id: 1,
        name: '张三',
        position: '技术总监',
        email: '<EMAIL>',
        avatar: '',
        status: 'online',
        tags: ['技术专家', '团队领导']
      },
      {
        id: 2,
        name: '李四',
        position: '高级工程师',
        email: '<EMAIL>',
        avatar: '',
        status: 'online',
        tags: ['前端专家', '架构师']
      },
      {
        id: 3,
        name: '王五',
        position: '工程师',
        email: '<EMAIL>',
        avatar: '',
        status: 'offline',
        tags: ['后端开发', '数据库']
      }
    ]
  },
  {
    id: 2,
    name: '销售部',
    description: '负责市场开拓和客户维护',
    icon: ShopOutlined,
    color: '#52c41a',
    employeeCount: 32,
    manager: '赵六',
    establishDate: '2019-06-20',
    children: [
      {
        id: 21,
        name: '华北区',
        icon: ShopOutlined,
        color: '#73d13d',
        employeeCount: 12
      },
      {
        id: 22,
        name: '华南区',
        icon: ShopOutlined,
        color: '#52c41a',
        employeeCount: 20
      }
    ],
    employees: [
      {
        id: 4,
        name: '赵六',
        position: '销售总监',
        email: '<EMAIL>',
        avatar: '',
        status: 'online',
        tags: ['销售专家', '客户关系']
      }
    ]
  },
  {
    id: 3,
    name: '财务部',
    description: '负责财务管理和成本控制',
    icon: DollarOutlined,
    color: '#faad14',
    employeeCount: 18,
    manager: '孙七',
    establishDate: '2019-03-10',
    children: [],
    employees: [
      {
        id: 5,
        name: '孙七',
        position: '财务总监',
        email: '<EMAIL>',
        avatar: '',
        status: 'online',
        tags: ['财务专家', 'CPA']
      }
    ]
  },
  {
    id: 4,
    name: '人事部',
    description: '负责人力资源管理和企业文化建设',
    icon: TeamOutlined,
    color: '#722ed1',
    employeeCount: 12,
    manager: '周八',
    establishDate: '2019-01-05',
    children: [],
    employees: [
      {
        id: 6,
        name: '周八',
        position: 'HR总监',
        email: '<EMAIL>',
        avatar: '',
        status: 'online',
        tags: ['人力资源', '企业文化']
      }
    ]
  }
])

// 部门树形数据
const departmentTreeData = computed(() => {
  const buildTree = (depts) => {
    return depts.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? buildTree(dept.children) : []
    }))
  }
  return buildTree(departments.value)
})

// 所有员工数据
const allEmployees = computed(() => {
  const employees = []
  departments.value.forEach(dept => {
    if (dept.employees) {
      employees.push(...dept.employees)
    }
  })
  return employees
})

// 事件处理函数
const expandAll = () => {
  console.log('展开全部')
}

const collapseAll = () => {
  console.log('收起全部')
}

const refreshOrgChart = () => {
  console.log('刷新组织架构')
}

const handleDepartmentClick = (department) => {
  selectedDepartment.value = department
  console.log('选择部门:', department)
}

const handleDepartmentAction = ({ key, domEvent }) => {
  const id = domEvent.target.closest('[data-id]')?.dataset.id
  console.log('部门操作:', key, id)
}

const handleAddEmployee = () => {
  if (selectedDepartment.value) {
    employeeForm.departmentId = selectedDepartment.value.id
  }
  showEmployeeModal.value = true
}

const handleEditDepartment = () => {
  if (selectedDepartment.value) {
    departmentForm.name = selectedDepartment.value.name
    departmentForm.description = selectedDepartment.value.description
    showDepartmentModal.value = true
  }
}

const handleEmployeeSearch = (value) => {
  console.log('搜索员工:', value)
}

const handleEmployeeAction = ({ key, domEvent }) => {
  const id = domEvent.target.closest('[data-id]')?.dataset.id
  console.log('员工操作:', key, id)
}

const handleCreateDepartment = () => {
  console.log('创建部门:', departmentForm)
  showDepartmentModal.value = false
}

const handleCreateEmployee = () => {
  console.log('创建员工:', employeeForm)
  showEmployeeModal.value = false
}

// 初始化选择第一个部门
if (departments.value.length > 0) {
  selectedDepartment.value = departments.value[0]
}
</script>

<style scoped>
/* 组织架构管理页面样式 */
.organization-management {
  padding: 0;
}

/* 组织架构图样式 */
.org-chart-section {
  margin-bottom: 24px;
}

.org-chart {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow-x: auto;
}

.org-node {
  position: relative;
  margin-bottom: 24px;
}

.root-node {
  text-align: center;
  margin-bottom: 40px;
}

.root-node .node-content {
  display: inline-block;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border-radius: 20px;
  padding: 24px 32px;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.3);
}

.node-content {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.node-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.node-avatar {
  flex-shrink: 0;
}

.node-info {
  flex: 1;
}

.node-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.root-node .node-title {
  color: white;
  font-size: 20px;
}

.node-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0 0 4px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.root-node .node-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.node-count {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.root-node .node-count {
  color: rgba(255, 255, 255, 0.9);
}

.node-actions {
  flex-shrink: 0;
}

.org-children {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 32px;
  position: relative;
}

.org-children::before {
  content: '';
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 16px;
  background: #d9d9d9;
}

.sub-node {
  margin-top: 16px;
}

.sub-node .node-content {
  padding: 16px;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.sub-node .node-title {
  font-size: 14px;
}

/* 部门详情样式 */
.department-detail-section {
  margin-bottom: 24px;
}

.detail-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.detail-header {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 24px;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.dept-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
}

.dept-text h3 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1890ff;
}

.dept-text p {
  font-size: 16px;
  color: #666;
  margin: 0 0 12px 0;
}

.dept-stats {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #8c8c8c;
}

.detail-actions {
  display: flex;
  gap: 12px;
}

/* 员工区域样式 */
.employees-section {
  padding: 24px;
}

.employees-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.employees-header h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #1890ff;
}

.employee-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(24, 144, 255, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.12);
}

.employee-avatar {
  position: relative;
  text-align: center;
  margin-bottom: 16px;
}

.employee-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.employee-status.online {
  background: #52c41a;
}

.employee-status.offline {
  background: #d9d9d9;
}

.employee-info {
  flex: 1;
  text-align: center;
}

.employee-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.employee-position {
  font-size: 14px;
  color: #1890ff;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.employee-email {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0 0 12px 0;
}

.employee-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  margin-bottom: 16px;
}

.employee-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .org-children {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .dept-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .org-chart {
    padding: 20px;
  }

  .org-children {
    grid-template-columns: 1fr;
  }

  .node-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .employees-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .dept-stats {
    flex-direction: column;
    gap: 8px;
  }

  .detail-actions {
    flex-direction: column;
    width: 100%;
  }
}
</style>
