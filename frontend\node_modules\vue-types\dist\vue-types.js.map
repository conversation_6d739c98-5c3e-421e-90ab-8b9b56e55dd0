{"version": 3, "file": "vue-types.js", "sources": ["../node_modules/is-plain-object/index.es.js", "../src/utils.ts", "../src/validators/native.ts", "../src/validators/custom.ts", "../src/validators/oneof.ts", "../src/validators/oneoftype.ts", "../src/validators/arrayof.ts", "../src/validators/instanceof.ts", "../src/validators/objectof.ts", "../src/validators/shape.ts", "../src/index.ts", "../src/sensibles.ts", "../src/index.cjs.ts"], "sourcesContent": ["/*!\n * isobject <https://github.com/jonschlinkert/isobject>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObject(val) {\n  return val != null && typeof val === 'object' && Array.isArray(val) === false;\n}\n\n/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObjectObject(o) {\n  return isObject(o) === true\n    && Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObjectObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (typeof ctor !== 'function') return false;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObjectObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n}\n\nexport default isPlainObject;\n", "import _isPlainObject from 'is-plain-object'\nimport {\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueProp,\n  InferType,\n  PropOptions,\n} from './types'\n\nconst ObjProto = Object.prototype\nconst toString = ObjProto.toString\nexport const hasOwn = ObjProto.hasOwnProperty\n\nconst FN_MATCH_REGEXP = /^\\s*function (\\w+)/\n\n// https://github.com/vuejs/vue/blob/dev/src/core/util/props.js#L177\nexport function getType(\n  fn: VueProp<any> | (() => any) | (new (...args: any[]) => any),\n): string {\n  const type = (fn as VueProp<any>)?.type ?? fn\n  if (type) {\n    const match = type.toString().match(FN_MATCH_REGEXP)\n    return match ? match[1] : ''\n  }\n  return ''\n}\n\nexport function getNativeType(value: any): string {\n  if (value === null || value === undefined) return ''\n  const match = value.constructor.toString().match(FN_MATCH_REGEXP)\n  return match ? match[1] : ''\n}\n\ntype PlainObject = { [key: string]: any }\nexport const isPlainObject = _isPlainObject as (obj: any) => obj is PlainObject\n\n/**\n * No-op function\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\n/**\n * A function that returns its first argument\n *\n * @param arg - Any argument\n */\nexport const identity = (arg: any) => arg\n\nlet warn: (msg: string) => string | void = identity\n\nif (process.env.NODE_ENV !== 'production') {\n  const hasConsole = typeof console !== 'undefined'\n  warn = hasConsole\n    ? function warn(msg) {\n        // eslint-disable-next-line no-console\n        console.warn(`[VueTypes warn]: ${msg}`)\n      }\n    : identity\n}\n\nexport { warn }\n\n/**\n * Checks for a own property in an object\n *\n * @param {object} obj - Object\n * @param {string} prop - Property to check\n */\nexport const has = <T extends any, U extends keyof T>(obj: T, prop: U) =>\n  hasOwn.call(obj, prop)\n\n/**\n * Determines whether the passed value is an integer. Uses `Number.isInteger` if available\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n * @param {*} value - The value to be tested for being an integer.\n * @returns {boolean}\n */\nexport const isInteger =\n  Number.isInteger ||\n  function isInteger(value: unknown): value is number {\n    return (\n      typeof value === 'number' &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    )\n  }\n\n/**\n * Determines whether the passed value is an Array.\n *\n * @param {*} value - The value to be tested for being an array.\n * @returns {boolean}\n */\nexport const isArray =\n  Array.isArray ||\n  function isArray(value): value is any[] {\n    return toString.call(value) === '[object Array]'\n  }\n\n/**\n * Checks if a value is a function\n *\n * @param {any} value - Value to check\n * @returns {boolean}\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isFunction = (value: unknown): value is Function =>\n  toString.call(value) === '[object Function]'\n\n/**\n * Checks if the passed-in value is a VueTypes type\n * @param value - The value to check\n */\nexport const isVueTypeDef = <T>(\n  value: any,\n): value is VueTypeDef<T> | VueTypeValidableDef<T> =>\n  isPlainObject(value) && has(value, '_vueTypes_name')\n\n/**\n * Checks if the passed-in value is a Vue prop definition object or a VueTypes type\n * @param value - The value to check\n */\nexport const isComplexType = <T>(value: any): value is VueProp<T> =>\n  isPlainObject(value) &&\n  (has(value, 'type') ||\n    ['_vueTypes_name', 'validator', 'default', 'required'].some((k) =>\n      has(value, k),\n    ))\n\nexport interface WrappedFn {\n  (...args: any[]): any\n  __original: (...args: any[]) => any\n}\n\n/**\n * Binds a function to a context and saves a reference to the original.\n *\n * @param fn - Target function\n * @param ctx - New function context\n */\nexport function bindTo(fn: (...args: any[]) => any, ctx: any): WrappedFn {\n  return Object.defineProperty(fn.bind(ctx), '__original', {\n    value: fn,\n  })\n}\n\n/**\n * Returns the original function bounded with `bindTo`. If the passed-in function\n * has not be bound, the function itself will be returned instead.\n *\n * @param fn - Function to unwrap\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function unwrap<T extends WrappedFn | Function>(fn: T) {\n  return (fn as WrappedFn).__original ?? fn\n}\n\n/**\n * Validates a given value against a prop type object.\n *\n * If `silent` is `false` (default) will return a boolean. If it is set to `true`\n * it will return `true` on success or a string error message on failure\n *\n * @param {Object|*} type - Type to use for validation. Either a type object or a constructor\n * @param {*} value - Value to check\n * @param {boolean} silent - Silence warnings\n */\nexport function validateType<T, U>(\n  type: T,\n  value: U,\n  silent = false,\n): string | boolean {\n  let typeToCheck: { [key: string]: any }\n  let valid = true\n  let expectedType = ''\n  if (!isPlainObject(type)) {\n    typeToCheck = { type }\n  } else {\n    typeToCheck = type\n  }\n  const namePrefix = isVueTypeDef(typeToCheck)\n    ? typeToCheck._vueTypes_name + ' - '\n    : ''\n\n  if (isComplexType(typeToCheck) && typeToCheck.type !== null) {\n    if (typeToCheck.type === undefined || typeToCheck.type === true) {\n      return valid\n    }\n    if (!typeToCheck.required && value === undefined) {\n      return valid\n    }\n    if (isArray(typeToCheck.type)) {\n      valid = typeToCheck.type.some(\n        (type: any) => validateType(type, value, true) === true,\n      )\n      expectedType = typeToCheck.type\n        .map((type: any) => getType(type))\n        .join(' or ')\n    } else {\n      expectedType = getType(typeToCheck)\n\n      if (expectedType === 'Array') {\n        valid = isArray(value)\n      } else if (expectedType === 'Object') {\n        valid = isPlainObject(value)\n      } else if (\n        expectedType === 'String' ||\n        expectedType === 'Number' ||\n        expectedType === 'Boolean' ||\n        expectedType === 'Function'\n      ) {\n        valid = getNativeType(value) === expectedType\n      } else {\n        valid = value instanceof typeToCheck.type\n      }\n    }\n  }\n\n  if (!valid) {\n    const msg = `${namePrefix}value \"${value}\" should be of type \"${expectedType}\"`\n    if (silent === false) {\n      warn(msg)\n      return false\n    }\n    return msg\n  }\n\n  if (has(typeToCheck, 'validator') && isFunction(typeToCheck.validator)) {\n    const oldWarn = warn\n    const warnLog = []\n    warn = (msg) => {\n      warnLog.push(msg)\n    }\n\n    valid = typeToCheck.validator(value)\n    warn = oldWarn\n\n    if (!valid) {\n      const msg = (warnLog.length > 1 ? '* ' : '') + warnLog.join('\\n* ')\n      warnLog.length = 0\n      if (silent === false) {\n        warn(msg)\n        return valid\n      }\n      return msg\n    }\n  }\n  return valid\n}\n\n/**\n * Adds `isRequired` and `def` modifiers to an object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toType<T = any>(name: string, obj: PropOptions<T>) {\n  const type: VueTypeDef<T> = Object.defineProperties(obj, {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n    def: {\n      value(def?: any) {\n        if (def === undefined && !this.default) {\n          return this\n        }\n        if (!isFunction(def) && validateType(this, def, true) !== true) {\n          warn(`${this._vueTypes_name} - invalid default value: \"${def}\"`)\n          return this\n        }\n        if (isArray(def)) {\n          this.default = () => [...def]\n        } else if (isPlainObject(def)) {\n          this.default = () => Object.assign({}, def)\n        } else {\n          this.default = def\n        }\n        return this\n      },\n    },\n  })\n\n  const { validator } = type\n  if (isFunction(validator)) {\n    type.validator = bindTo(validator, type)\n  }\n\n  return type\n}\n\n/**\n * Like `toType` but also adds the `validate()` method to the type object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toValidableType<T = any>(name: string, obj: PropOptions<T>) {\n  const type = toType<T>(name, obj)\n  return Object.defineProperty(type, 'validate', {\n    value(fn: (value: T) => boolean) {\n      if (isFunction(this.validator)) {\n        warn(\n          `${\n            this._vueTypes_name\n          } - calling .validate() will overwrite the current custom validator function. Validator info:\\n${JSON.stringify(\n            this,\n          )}`,\n        )\n      }\n      this.validator = bindTo(fn, this)\n      return this\n    },\n  }) as VueTypeValidableDef<T>\n}\n\n/**\n *  Clones an object preserving all of it's own keys.\n *\n * @param obj - Object to clone\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function clone<T extends object>(obj: T): T {\n  const descriptors = {} as { [P in keyof T]: any }\n  Object.getOwnPropertyNames(obj).forEach((key) => {\n    descriptors[key as keyof T] = Object.getOwnPropertyDescriptor(obj, key)\n  })\n  return Object.defineProperties({}, descriptors)\n}\n\n/**\n * Return a new VueTypes type using another type as base.\n *\n * Properties in the `props` object will overwrite those defined in the source one\n * expect for the `validator` function. In that case both functions will be executed in series.\n *\n * @param name - Name of the new type\n * @param source - Source type\n * @param props - Custom type properties\n */\nexport function fromType<T extends VueTypeDef<any>>(name: string, source: T): T\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>\n>(name: string, source: T, props: V): Omit<T, keyof V> & V\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>\n>(name: string, source: T, props?: V) {\n  // 1. create an exact copy of the source type\n  const copy = clone(source)\n\n  // 2. give it a new name\n  copy._vueTypes_name = name\n\n  if (!isPlainObject(props)) {\n    return copy\n  }\n  const { validator, ...rest } = props\n\n  // 3. compose the validator function\n  // with the one on the source (if present)\n  // and ensure it is bound to the copy\n  if (isFunction(validator)) {\n    let { validator: prevValidator } = copy\n\n    if (prevValidator) {\n      prevValidator = unwrap(prevValidator)\n    }\n\n    copy.validator = bindTo(\n      prevValidator\n        ? function (this: T, value: any) {\n            return (\n              prevValidator.call(this, value) && validator.call(this, value)\n            )\n          }\n        : validator,\n      copy,\n    )\n  }\n  // 4. overwrite the rest, if present\n  return Object.assign(copy, rest as V)\n}\n\nexport function indent(string: string) {\n  return string.replace(/^(?!\\s*$)/gm, '  ')\n}\n", "import { toType, toValidableType, isInteger } from '../utils'\nimport { PropType } from '../types'\n\nexport const any = () => toValidableType('any', {})\n\nexport const func = <T extends (...args: any[]) => any>() =>\n  toValidableType<T>('function', {\n    type: Function as PropType<T>,\n  })\n\nexport const bool = () =>\n  toValidableType('boolean', {\n    type: Boolean,\n  })\n\nexport const string = () =>\n  toValidableType('string', {\n    type: String,\n  })\n\nexport const number = () =>\n  toValidableType('number', {\n    type: Number,\n  })\n\nexport const array = <T>() =>\n  toValidableType<T[]>('array', {\n    type: Array,\n  })\n\nexport const object = <T extends { [key: string]: any }>() =>\n  toValidableType<T>('object', {\n    type: Object,\n  })\n\nexport const integer = () =>\n  toType('integer', {\n    type: Number,\n    validator(value) {\n      return isInteger(value)\n    },\n  })\n\nexport const symbol = () =>\n  toType<symbol>('symbol', {\n    validator(value) {\n      return typeof value === 'symbol'\n    },\n  })\n", "import { toType, warn } from '../utils'\nimport { ValidatorFunction, VueTypeDef } from '../types'\n\nexport default function custom<T>(\n  validatorFn: ValidatorFunction<T>,\n  warnMsg = 'custom validation failed',\n) {\n  if (typeof validatorFn !== 'function') {\n    throw new TypeError(\n      '[VueTypes error]: You must provide a function as argument',\n    )\n  }\n\n  return toType<T>(validatorFn.name || '<<anonymous function>>', {\n    validator(this: VueTypeDef<T>, value: T) {\n      const valid = validatorFn(value)\n      if (!valid) warn(`${this._vueTypes_name} - ${warnMsg}`)\n      return valid\n    },\n  })\n}\n", "import { Prop } from '../types'\nimport { toType, warn, isArray } from '../utils'\n\nexport default function oneOf<T extends readonly any[]>(arr: T) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument.',\n    )\n  }\n  const msg = `oneOf - value should be one of \"${arr.join('\", \"')}\".`\n  const allowedTypes = arr.reduce((ret, v) => {\n    if (v !== null && v !== undefined) {\n      const constr = (v as any).constructor\n      ret.indexOf(constr) === -1 && ret.push(constr)\n    }\n    return ret\n  }, [] as Prop<T[number]>[])\n\n  return toType<T[number]>('oneOf', {\n    type: allowedTypes.length > 0 ? allowedTypes : undefined,\n    validator(value) {\n      const valid = arr.indexOf(value) !== -1\n      if (!valid) warn(msg)\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType, PropType } from '../types'\nimport {\n  isArray,\n  isComplexType,\n  isVueTypeDef,\n  isFunction,\n  toType,\n  validateType,\n  warn,\n  indent,\n} from '../utils'\n\nexport default function oneOfType<\n  U extends VueProp<any> | Prop<any>,\n  V = InferType<U>\n>(arr: U[]) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument',\n    )\n  }\n\n  let hasCustomValidators = false\n\n  let nativeChecks: Prop<V>[] = []\n\n  for (let i = 0; i < arr.length; i += 1) {\n    const type = arr[i]\n    if (isComplexType<V>(type)) {\n      if (isVueTypeDef<V>(type) && type._vueTypes_name === 'oneOf') {\n        nativeChecks = nativeChecks.concat(type.type as PropType<V>)\n        continue\n      }\n      if (isFunction(type.validator)) {\n        hasCustomValidators = true\n      }\n      if (type.type !== true && type.type) {\n        nativeChecks = nativeChecks.concat(type.type)\n        continue\n      }\n    }\n    nativeChecks.push(type as Prop<V>)\n  }\n\n  // filter duplicates\n  nativeChecks = nativeChecks.filter((t, i) => nativeChecks.indexOf(t) === i)\n\n  if (!hasCustomValidators) {\n    // we got just native objects (ie: Array, Object)\n    // delegate to Vue native prop check\n    return toType<V>('oneOfType', {\n      type: nativeChecks,\n    })\n  }\n\n  return toType<V>('oneOfType', {\n    type: nativeChecks,\n    validator(value) {\n      const err: string[] = []\n      const valid = arr.some((type) => {\n        const t =\n          isVueTypeDef(type) && type._vueTypes_name === 'oneOf'\n            ? type.type || null\n            : type\n        const res = validateType(t, value, true)\n        if (typeof res === 'string') {\n          err.push(res)\n        }\n        return res === true\n      })\n      if (!valid) {\n        warn(\n          `oneOfType - provided value does not match any of the ${\n            err.length\n          } passed-in validators:\\n${indent(err.join('\\n'))}`,\n        )\n      }\n\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function arrayOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<InferType<T>[]>('arrayOf', {\n    type: Array,\n    validator(values: any[]) {\n      let vResult: string | boolean\n      const valid = values.every((value) => {\n        vResult = validateType(type, value, true)\n        return vResult === true\n      })\n      if (!valid) {\n        warn(`arrayOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { toType } from '../utils'\nimport { Constructor } from '../types'\n\nexport default function instanceOf<C extends Constructor>(\n  instanceConstructor: C,\n) {\n  return toType<InstanceType<C>>('instanceOf', {\n    type: instanceConstructor,\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function objectOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<{ [key: string]: InferType<T> }>('objectOf', {\n    type: Object,\n    validator(obj) {\n      let vResult: string | boolean\n      const valid = Object.keys(obj).every((key) => {\n        vResult = validateType(type, obj[key], true)\n        return vResult === true\n      })\n\n      if (!valid) {\n        warn(`objectOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { <PERSON>p, VueProp, VueType<PERSON>hape, VueTypeLooseShape } from '../types'\nimport { toType, validateType, warn, isPlainObject, indent } from '../utils'\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport default function shape<T extends object>(\n  obj: { [K in keyof T]: Prop<T[K]> | VueProp<T[K]> },\n): VueTypeShape<T> {\n  const keys = Object.keys(obj)\n  const requiredKeys = keys.filter((key) => !!(obj as any)[key]?.required)\n\n  const type = toType('shape', {\n    type: Object,\n    validator(this: VueTypeShape<T> | VueTypeLooseShape<T>, value) {\n      if (!isPlainObject(value)) {\n        return false\n      }\n      const valueKeys = Object.keys(value)\n\n      // check for required keys (if any)\n      if (\n        requiredKeys.length > 0 &&\n        requiredKeys.some((req) => valueKeys.indexOf(req) === -1)\n      ) {\n        const missing = requiredKeys.filter(\n          (req) => valueKeys.indexOf(req) === -1,\n        )\n        if (missing.length === 1) {\n          warn(`shape - required property \"${missing[0]}\" is not defined.`)\n        } else {\n          warn(\n            `shape - required properties \"${missing.join(\n              '\", \"',\n            )}\" are not defined.`,\n          )\n        }\n\n        return false\n      }\n\n      return valueKeys.every((key) => {\n        if (keys.indexOf(key) === -1) {\n          if ((this as VueTypeLooseShape<T>)._vueTypes_isLoose === true)\n            return true\n          warn(\n            `shape - shape definition does not include a \"${key}\" property. Allowed keys: \"${keys.join(\n              '\", \"',\n            )}\".`,\n          )\n          return false\n        }\n        const type = (obj as any)[key]\n        const valid = validateType(type, value[key], true)\n        if (typeof valid === 'string') {\n          warn(`shape - \"${key}\" property validation error:\\n ${indent(valid)}`)\n        }\n        return valid === true\n      })\n    },\n  }) as VueTypeShape<T>\n\n  Object.defineProperty(type, '_vueTypes_isLoose', {\n    writable: true,\n    value: false,\n  })\n\n  Object.defineProperty(type, 'loose', {\n    get() {\n      this._vueTypes_isLoose = true\n      return this\n    },\n  })\n\n  return type\n}\n", "import {\n  toType,\n  toValidableType,\n  validateType,\n  isArray,\n  isVueTypeDef,\n  has,\n  fromType,\n} from './utils'\n\nimport {\n  VueTypesDefaults,\n  ExtendProps,\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueTypeShape,\n  VueTypeLooseShape,\n} from './types'\nimport { typeDefaults } from './sensibles'\nimport { PropOptions } from './types'\n\nimport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n} from './validators/native'\nimport custom from './validators/custom'\nimport oneOf from './validators/oneof'\nimport oneOfType from './validators/oneoftype'\nimport arrayOf from './validators/arrayof'\nimport instanceOf from './validators/instanceof'\nimport objectOf from './validators/objectof'\nimport shape from './validators/shape'\n\nclass BaseVueTypes {\n  static defaults: Partial<VueTypesDefaults> = {}\n\n  static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n  static get any() {\n    return any()\n  }\n  static get func() {\n    return func().def(this.defaults.func)\n  }\n  static get bool() {\n    return bool().def(this.defaults.bool)\n  }\n  static get string() {\n    return string().def(this.defaults.string)\n  }\n  static get number() {\n    return number().def(this.defaults.number)\n  }\n  static get array() {\n    return array().def(this.defaults.array)\n  }\n  static get object() {\n    return object().def(this.defaults.object)\n  }\n  static get integer() {\n    return integer().def(this.defaults.integer)\n  }\n  static get symbol() {\n    return symbol()\n  }\n\n  static readonly custom = custom\n  static readonly oneOf = oneOf\n  static readonly instanceOf = instanceOf\n  static readonly oneOfType = oneOfType\n  static readonly arrayOf = arrayOf\n  static readonly objectOf = objectOf\n  static readonly shape = shape\n\n  static extend<T>(props: ExtendProps | ExtendProps[]): T {\n    if (isArray(props)) {\n      props.forEach((p) => this.extend(p))\n      return this as any\n    }\n\n    const { name, validate = false, getter = false, ...opts } = props\n\n    if (has(this, name as any)) {\n      throw new TypeError(`[VueTypes error]: Type \"${name}\" already defined`)\n    }\n\n    const { type } = opts\n    if (isVueTypeDef(type)) {\n      // we are using as base type a vue-type object\n\n      // detach the original type\n      // we are going to inherit the parent data.\n      delete opts.type\n\n      if (getter) {\n        return Object.defineProperty(this, name, {\n          get: () => fromType(name, type, opts as Omit<ExtendProps, 'type'>),\n        })\n      }\n      return Object.defineProperty(this, name, {\n        value(...args: unknown[]) {\n          const t = fromType(name, type, opts as Omit<ExtendProps, 'type'>)\n          if (t.validator) {\n            t.validator = t.validator.bind(t, ...args)\n          }\n          return t\n        },\n      })\n    }\n\n    let descriptor: PropertyDescriptor\n    if (getter) {\n      descriptor = {\n        get() {\n          const typeOptions = Object.assign({}, opts as PropOptions<T>)\n          if (validate) {\n            return toValidableType<T>(name, typeOptions)\n          }\n          return toType<T>(name, typeOptions)\n        },\n        enumerable: true,\n      }\n    } else {\n      descriptor = {\n        value(...args: T[]) {\n          const typeOptions = Object.assign({}, opts as PropOptions<T>)\n          let ret: VueTypeDef<T>\n          if (validate) {\n            ret = toValidableType<T>(name, typeOptions)\n          } else {\n            ret = toType<T>(name, typeOptions)\n          }\n\n          if (typeOptions.validator) {\n            ret.validator = typeOptions.validator.bind(ret, ...args)\n          }\n          return ret\n        },\n        enumerable: true,\n      }\n    }\n\n    return Object.defineProperty(this, name, descriptor)\n  }\n\n  static utils = {\n    validate<T, U>(value: T, type: U) {\n      return validateType<U, T>(type, value, true) === true\n    },\n    toType<T = unknown>(\n      name: string,\n      obj: PropOptions<T>,\n      validable = false,\n    ): VueTypeDef<T> | VueTypeValidableDef<T> {\n      return validable ? toValidableType<T>(name, obj) : toType<T>(name, obj)\n    },\n  }\n}\n\nfunction createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\nexport default class VueTypes extends createTypes() {}\n\nexport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  custom,\n  oneOf,\n  oneOfType,\n  arrayOf,\n  instanceOf,\n  objectOf,\n  shape,\n  createTypes,\n  toType,\n  toValidableType,\n  validateType,\n  fromType,\n}\n\nexport type VueTypesInterface = ReturnType<typeof createTypes>\nexport { VueTypeDef, VueTypeValidableDef, VueTypeShape, VueTypeLooseShape }\n", "import { VueTypesDefaults } from './types'\n\nexport const typeDefaults = (): VueTypesDefaults => ({\n  func: () => undefined,\n  bool: true,\n  string: '',\n  number: 0,\n  array: () => [],\n  object: () => ({}),\n  integer: 0,\n})\n", "import VueTypes from './index'\nObject.defineProperty(exports, '__esModule', {\n  value: true,\n})\n\nexport default VueTypes\n\nexport * from './index'\n"], "names": ["isObjectObject", "o", "val", "Array", "isArray", "Object", "prototype", "toString", "call", "Obj<PERSON><PERSON><PERSON>", "hasOwn", "hasOwnProperty", "FN_MATCH_REGEXP", "getType", "fn", "type", "match", "isPlainObject", "ctor", "prot", "constructor", "identity", "arg", "warn", "process", "env", "NODE_ENV", "hasConsole", "console", "msg", "has", "obj", "prop", "isInteger", "Number", "value", "isFinite", "Math", "floor", "isFunction", "isVueTypeDef", "isComplexType", "some", "k", "bindTo", "ctx", "defineProperty", "bind", "validateType", "silent", "typeToCheck", "valid", "expectedType", "namePrefix", "_vueTypes_name", "undefined", "required", "map", "join", "getNativeType", "validator", "old<PERSON>arn", "warnLog", "push", "length", "toType", "name", "defineProperties", "writable", "isRequired", "get", "this", "def", "default", "assign", "toValidableType", "JSON", "stringify", "fromType", "source", "props", "descriptors", "copy", "getOwnPropertyNames", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "rest", "prevValidator", "__original", "indent", "string", "replace", "any", "func", "Function", "bool", "Boolean", "String", "number", "array", "object", "integer", "symbol", "custom", "validatorFn", "warnMsg", "TypeError", "oneOf", "arr", "allowedTypes", "reduce", "ret", "v", "constr", "indexOf", "oneOfType", "hasCustomValidators", "nativeChecks", "i", "concat", "filter", "t", "err", "res", "arrayOf", "values", "vResult", "every", "instanceOf", "instanceConstructor", "objectOf", "keys", "shape", "requiredKeys", "_obj$key", "valueKeys", "req", "missing", "_this", "_vueTypes_isLoose", "BaseVueTypes", "extend", "p", "validate", "getter", "opts", "descriptor", "typeOptions", "enumerable", "defaults", "createTypes", "defs", "validable", "VueTypes", "exports"], "mappings": "ipBAkBA,SAASA,EAAeC,GACtB,OAAuB,IAXT,OADEC,EAYAD,IAXqB,iBAARC,IAA2C,IAAvBC,MAAMC,QAAQF,KAYpB,oBAAtCG,OAAOC,UAAUC,SAASC,KAAKP,GAbtC,IAAkBC,ECElB,IAAMO,EAAWJ,OAAOC,UAClBC,EAAWE,EAASF,SACbG,EAASD,EAASE,eAEzBC,EAAkB,qBAGxB,SAAgBC,EACdC,SAEMC,YAAQD,MAAAA,SAAAA,EAAqBC,oBAAQD,EAC3C,GAAIC,EAAM,CACR,IAAMC,EAAQD,EAAKR,WAAWS,MAAMJ,GACpC,OAAOI,EAAQA,EAAM,GAAK,GAE5B,MAAO,OAUIC,EDXb,SAAuBhB,GACrB,IAAIiB,EAAKC,EAET,OAA0B,IAAtBnB,EAAeC,IAIC,mBADpBiB,EAAOjB,EAAEmB,eAKoB,IAAzBpB,EADJmB,EAAOD,EAAKZ,aAIiC,IAAzCa,EAAKR,eAAe,kBCUbU,EAAW,SAACC,UAAaA,GAElCC,EAAuCF,EAE3C,GAA6B,eAAzBG,QAAQC,IAAIC,SAA2B,CACzC,IAAMC,EAAgC,oBAAZC,QAC1BL,EAAOI,EACH,SAAcE,GAEZD,QAAQL,yBAAyBM,IAEnCR,MAWOS,EAAM,SAAmCC,EAAQC,UAC5DtB,EAAOF,KAAKuB,EAAKC,IASNC,EACXC,OAAOD,WACP,SAAmBE,GACjB,MACmB,iBAAVA,GACPC,SAASD,IACTE,KAAKC,MAAMH,KAAWA,GAUf/B,EACXD,MAAMC,SACN,SAAiB+B,GACf,MAAgC,mBAAzB5B,EAASC,KAAK2B,IAUZI,EAAa,SAACJ,SACA,sBAAzB5B,EAASC,KAAK2B,IAMHK,EAAe,SAC1BL,UAEAlB,EAAckB,IAAUL,EAAIK,EAAO,mBAMxBM,EAAgB,SAAIN,UAC/BlB,EAAckB,KACbL,EAAIK,EAAO,SACV,CAAC,iBAAkB,YAAa,UAAW,YAAYO,KAAK,SAACC,UAC3Db,EAAIK,EAAOQ,OAcjB,SAAgBC,EAAO9B,EAA6B+B,GAClD,OAAOxC,OAAOyC,eAAehC,EAAGiC,KAAKF,GAAM,aAAc,CACvDV,MAAOrB,IAyBX,SAAgBkC,EACdjC,EACAoB,EACAc,GAEA,IAAIC,WAFJD,IAAAA,GAAS,GAGT,IAAIE,GAAQ,EACRC,EAAe,GAIjBF,EAHGjC,EAAcF,GAGHA,EAFA,CAAEA,KAAAA,GAIlB,IAAMsC,EAAab,EAAaU,GAC5BA,EAAYI,eAAiB,MAC7B,GAEJ,GAAIb,EAAcS,IAAqC,OAArBA,EAAYnC,KAAe,CAC3D,QAAyBwC,IAArBL,EAAYnC,OAA2C,IAArBmC,EAAYnC,KAChD,OAAOoC,EAET,IAAKD,EAAYM,eAAsBD,IAAVpB,EAC3B,OAAOgB,EAEL/C,EAAQ8C,EAAYnC,OACtBoC,EAAQD,EAAYnC,KAAK2B,KACvB,SAAC3B,UAAkD,IAApCiC,EAAajC,EAAMoB,GAAO,KAE3CiB,EAAeF,EAAYnC,KACxB0C,IAAI,SAAC1C,UAAcF,EAAQE,KAC3B2C,KAAK,SAKNP,EADmB,WAFrBC,EAAevC,EAAQqC,IAGb9C,EAAQ+B,GACU,WAAjBiB,EACDnC,EAAckB,GAEL,WAAjBiB,GACiB,WAAjBA,GACiB,YAAjBA,GACiB,aAAjBA,WAxLsBjB,GAC5B,GAAIA,MAAAA,EAAuC,MAAO,GAClD,IAAMnB,EAAQmB,EAAMf,YAAYb,WAAWS,MAAMJ,GACjD,OAAOI,EAAQA,EAAM,GAAK,GAuLZ2C,CAAcxB,KAAWiB,EAEzBjB,aAAiBe,EAAYnC,KAK3C,IAAKoC,EAAO,CACV,IAAMtB,EAASwB,YAAoBlB,0BAA6BiB,MAChE,OAAe,IAAXH,GACF1B,EAAKM,OAGAA,EAGT,GAAIC,EAAIoB,EAAa,cAAgBX,EAAWW,EAAYU,WAAY,CACtE,IAAMC,EAAUtC,EACVuC,EAAU,GAQhB,GAPAvC,EAAO,SAACM,GACNiC,EAAQC,KAAKlC,IAGfsB,EAAQD,EAAYU,UAAUzB,GAC9BZ,EAAOsC,GAEFV,EAAO,CACV,IAAMtB,GAAOiC,EAAQE,OAAS,EAAI,KAAO,IAAMF,EAAQJ,KAAK,QAE5D,OADAI,EAAQE,OAAS,GACF,IAAXf,GACF1B,EAAKM,GACEsB,GAEFtB,GAGX,OAAOsB,WASOc,EAAgBC,EAAcnC,GAC5C,IAAMhB,EAAsBV,OAAO8D,iBAAiBpC,EAAK,CACvDuB,eAAgB,CACdnB,MAAO+B,EACPE,UAAU,GAEZC,WAAY,CACVC,eAEE,OADAC,KAAKf,UAAW,SAIpBgB,IAAK,CACHrC,eAAMqC,GACJ,YAAYjB,IAARiB,GAAsBD,KAAKE,QAG1BlC,EAAWiC,KAA0C,IAAlCxB,EAAauB,KAAMC,GAAK,IAK9CD,KAAKE,QADHrE,EAAQoE,GACK,2BAAUA,IAChBvD,EAAcuD,GACR,kBAAMnE,OAAOqE,OAAO,GAAIF,IAExBA,SARfjD,EAAQgD,KAAKjB,6CAA4CkB,sBAezDZ,EAAc7C,EAAd6C,UAKR,OAJIrB,EAAWqB,KACb7C,EAAK6C,UAAYhB,EAAOgB,EAAW7C,IAG9BA,WASO4D,EAAyBT,EAAcnC,GACrD,IAAMhB,EAAOkD,EAAUC,EAAMnC,GAC7B,OAAO1B,OAAOyC,eAAe/B,EAAM,WAAY,CAC7CoB,eAAMrB,GAWJ,OAVIyB,EAAWgC,KAAKX,YAClBrC,EAEIgD,KAAKjB,gHAC0FsB,KAAKC,UACpGN,OAINA,KAAKX,UAAYhB,EAAO9B,EAAIyD,cAmClC,SAAgBO,EAGdZ,EAAca,EAAWC,GAEzB,IA5BsCjD,EAChCkD,EA2BAC,GA5BgCnD,EA4BnBgD,EA3BbE,EAAc,GACpB5E,OAAO8E,oBAAoBpD,GAAKqD,QAAQ,SAACC,GACvCJ,EAAYI,GAAkBhF,OAAOiF,yBAAyBvD,EAAKsD,KAE9DhF,OAAO8D,iBAAiB,GAAIc,IA4BnC,GAFAC,EAAK5B,eAAiBY,GAEjBjD,EAAc+D,GACjB,OAAOE,MAjN4CpE,IAmN7C8C,EAAuBoB,EAAvBpB,UAAc2B,IAASP,iBAK/B,GAAIzC,EAAWqB,GAAY,KACR4B,EAAkBN,EAA7BtB,UAEF4B,IACFA,aA5NiD1E,EA4N1B0E,GA3NFC,0BAAc3E,GA8NrCoE,EAAKtB,UAAYhB,EACf4C,EACI,SAAmBrD,GACjB,OACEqD,EAAchF,KAAK+D,KAAMpC,IAAUyB,EAAUpD,KAAK+D,KAAMpC,IAG5DyB,EACJsB,GAIJ,OAAO7E,OAAOqE,OAAOQ,EAAMK,YAGbG,EAAOC,GACrB,OAAOA,EAAOC,QAAQ,cAAe,UCvY1BC,EAAM,kBAAMlB,EAAgB,MAAO,KAEnCmB,EAAO,kBAClBnB,EAAmB,WAAY,CAC7B5D,KAAMgF,YAGGC,EAAO,kBAClBrB,EAAgB,UAAW,CACzB5D,KAAMkF,WAGGN,EAAS,kBACpBhB,EAAgB,SAAU,CACxB5D,KAAMmF,UAGGC,EAAS,kBACpBxB,EAAgB,SAAU,CACxB5D,KAAMmB,UAGGkE,EAAQ,kBACnBzB,EAAqB,QAAS,CAC5B5D,KAAMZ,SAGGkG,EAAS,kBACpB1B,EAAmB,SAAU,CAC3B5D,KAAMV,UAGGiG,EAAU,kBACrBrC,EAAO,UAAW,CAChBlD,KAAMmB,OACN0B,mBAAUzB,GACR,OAAOF,EAAUE,OAIVoE,EAAS,kBACpBtC,EAAe,SAAU,CACvBL,mBAAUzB,GACR,MAAwB,iBAAVA,eC3CIqE,EACtBC,EACAC,GAEA,YAFAA,IAAAA,EAAU,4BAEiB,mBAAhBD,EACT,UAAUE,UACR,6DAIJ,OAAO1C,EAAUwC,EAAYvC,MAAQ,yBAA0B,CAC7DN,mBAA+BzB,GAC7B,IAAMgB,EAAQsD,EAAYtE,GAE1B,OADKgB,GAAO5B,EAAQgD,KAAKjB,qBAAoBoD,GACtCvD,cCdWyD,EAAgCC,GACtD,IAAKzG,EAAQyG,GACX,UAAUF,UACR,4DAGJ,IAAM9E,qCAAyCgF,EAAInD,KAAK,aAClDoD,EAAeD,EAAIE,OAAO,SAACC,EAAKC,GACpC,GAAIA,MAAAA,EAA+B,CACjC,IAAMC,EAAUD,EAAU7F,aACD,IAAzB4F,EAAIG,QAAQD,IAAkBF,EAAIjD,KAAKmD,GAEzC,OAAOF,GACN,IAEH,OAAO/C,EAAkB,QAAS,CAChClD,KAAM+F,EAAa9C,OAAS,EAAI8C,OAAevD,EAC/CK,mBAAUzB,GACR,IAAMgB,GAAgC,IAAxB0D,EAAIM,QAAQhF,GAE1B,OADKgB,GAAO5B,EAAKM,GACVsB,cCXWiE,EAGtBP,GACA,IAAKzG,EAAQyG,GACX,UAAUF,UACR,2DAQJ,IAJA,IAAIU,GAAsB,EAEtBC,EAA0B,GAErBC,EAAI,EAAGA,EAAIV,EAAI7C,OAAQuD,GAAK,EAAG,CACtC,IAAMxG,EAAO8F,EAAIU,GACjB,GAAI9E,EAAiB1B,GAAO,CAC1B,GAAIyB,EAAgBzB,IAAiC,UAAxBA,EAAKuC,eAA4B,CAC5DgE,EAAeA,EAAaE,OAAOzG,EAAKA,MACxC,SAKF,GAHIwB,EAAWxB,EAAK6C,aAClByD,GAAsB,IAEN,IAAdtG,EAAKA,MAAiBA,EAAKA,KAAM,CACnCuG,EAAeA,EAAaE,OAAOzG,EAAKA,MACxC,UAGJuG,EAAavD,KAAKhD,GAMpB,OAFAuG,EAAeA,EAAaG,OAAO,SAACC,EAAGH,UAAMD,EAAaH,QAAQO,KAAOH,IAUlEtD,EAAU,YARZoD,EAQyB,CAC5BtG,KAAMuG,EACN1D,mBAAUzB,GACR,IAAMwF,EAAgB,GAChBxE,EAAQ0D,EAAInE,KAAK,SAAC3B,GACtB,IAIM6G,EAAM5E,EAHVR,EAAazB,IAAiC,UAAxBA,EAAKuC,eACvBvC,EAAKA,MAAQ,KACbA,EACsBoB,GAAO,GAInC,MAHmB,iBAARyF,GACTD,EAAI5D,KAAK6D,IAEI,IAARA,IAUT,OARKzE,GACH5B,0DAEIoG,EAAI3D,kCACqB0B,EAAOiC,EAAIjE,KAAK,QAIxCP,IA5BqB,CAC5BpC,KAAMuG,aChDYO,EAA4C9G,GAClE,OAAOkD,EAAuB,UAAW,CACvClD,KAAMZ,MACNyD,mBAAUkE,GACR,IAAIC,EACE5E,EAAQ2E,EAAOE,MAAM,SAAC7F,GAE1B,OAAmB,KADnB4F,EAAU/E,EAAajC,EAAMoB,GAAO,MAMtC,OAHKgB,GACH5B,wCAA2CmE,EAAOqC,IAE7C5E,cCZW8E,EACtBC,GAEA,OAAOjE,EAAwB,aAAc,CAC3ClD,KAAMmH,aCJcC,EAA6CpH,GACnE,OAAOkD,EAAwC,WAAY,CACzDlD,KAAMV,OACNuD,mBAAU7B,GACR,IAAIgG,EACE5E,EAAQ9C,OAAO+H,KAAKrG,GAAKiG,MAAM,SAAC3C,GAEpC,OAAmB,KADnB0C,EAAU/E,EAAajC,EAAMgB,EAAIsD,IAAM,MAOzC,OAHKlC,GACH5B,yCAA4CmE,EAAOqC,IAE9C5E,cCZWkF,EACtBtG,GAEA,IAAMqG,EAAO/H,OAAO+H,KAAKrG,GACnBuG,EAAeF,EAAKX,OAAO,SAACpC,4BAAWtD,EAAYsD,uBAAZkD,EAAkB/E,YAEzDzC,EAAOkD,EAAO,QAAS,CAC3BlD,KAAMV,OACNuD,mBAAwDzB,cACtD,IAAKlB,EAAckB,GACjB,SAEF,IAAMqG,EAAYnI,OAAO+H,KAAKjG,GAG9B,GACEmG,EAAatE,OAAS,GACtBsE,EAAa5F,KAAK,SAAC+F,UAAoC,IAA5BD,EAAUrB,QAAQsB,KAC7C,CACA,IAAMC,EAAUJ,EAAab,OAC3B,SAACgB,UAAoC,IAA5BD,EAAUrB,QAAQsB,KAY7B,OATElH,EADqB,IAAnBmH,EAAQ1E,qCACyB0E,EAAQ,uDAGTA,EAAQhF,KACtC,iCAQR,OAAO8E,EAAUR,MAAM,SAAC3C,GACtB,IAA2B,IAAvB+C,EAAKjB,QAAQ9B,GACf,OAAyD,IAApDsD,EAA8BC,oBAEnCrH,kDACkD8D,gCAAiC+C,EAAK1E,KACpF,kBAKN,IACMP,EAAQH,EADAjB,EAAYsD,GACOlD,EAAMkD,IAAM,GAI7C,MAHqB,iBAAVlC,GACT5B,cAAiB8D,oCAAqCK,EAAOvC,KAE9C,IAAVA,OAiBb,OAZA9C,OAAOyC,eAAe/B,EAAM,oBAAqB,CAC/CqD,UAAU,EACVjC,OAAO,IAGT9B,OAAOyC,eAAe/B,EAAM,QAAS,CACnCuD,eAEE,OADAC,KAAKqE,mBAAoB,UAKtB7H,MChCH8H,oCAyCGC,OAAP,SAAiB9D,cACf,GAAI5E,EAAQ4E,GAEV,OADAA,EAAMI,QAAQ,SAAC2D,UAAMJ,EAAKG,OAAOC,cAI3B7E,EAAoDc,EAApDd,OAAoDc,EAA9CgE,SAAAA,kBAA8ChE,EAA5BiE,OAAAA,gBAAmBC,IAASlE,gCAE5D,GAAIlD,EAAIyC,KAAML,GACZ,UAAUyC,qCAAqCzC,2BA2B7CiF,EAxBIpI,EAASmI,EAATnI,KACR,OAAIyB,EAAazB,WAKRmI,EAAKnI,KAGHV,OAAOyC,eAAeyB,KAAML,EADjC+E,EACuC,CACvC3E,IAAK,kBAAMQ,EAASZ,EAAMnD,EAAMmI,KAGK,CACvC/G,iBACE,MAAMuF,EAAI5C,EAASZ,EAAMnD,EAAMmI,GAI/B,OAHIxB,EAAE9D,YACJ8D,EAAE9D,aAAY8D,EAAE9D,WAAUb,cAAK2E,sCAE1BA,OAOXyB,EADEF,EACW,CACX3E,eACE,IAAM8E,EAAc/I,OAAOqE,OAAO,GAAIwE,GACtC,OAAIF,EACKrE,EAAmBT,EAAMkF,GAE3BnF,EAAUC,EAAMkF,IAEzBC,YAAY,GAGD,CACXlH,iBACE,IACI6E,IADEoC,EAAc/I,OAAOqE,OAAO,GAAIwE,GAWtC,OARElC,EADEgC,EACIrE,EAAmBT,EAAMkF,GAEzBnF,EAAUC,EAAMkF,GAGpBA,EAAYxF,YACdoD,EAAIpD,aAAYwF,EAAYxF,WAAUb,cAAKiE,sCAEtCA,GAETqC,YAAY,GAIThJ,OAAOyC,eAAeyB,KAAML,EAAMiF,yCAvGzC,OAAOtD,iCAGP,OAAOC,IAAOtB,IAAID,KAAK+E,SAASxD,mCAGhC,OAAOE,IAAOxB,IAAID,KAAK+E,SAAStD,qCAGhC,OAAOL,IAASnB,IAAID,KAAK+E,SAAS3D,uCAGlC,OAAOQ,IAAS3B,IAAID,KAAK+E,SAASnD,sCAGlC,OAAOC,IAAQ5B,IAAID,KAAK+E,SAASlD,sCAGjC,OAAOC,IAAS7B,IAAID,KAAK+E,SAASjD,wCAGlC,OAAOC,IAAU9B,IAAID,KAAK+E,SAAShD,wCAGnC,OAAOC,aAgGX,SAASgD,EAAYC,SACnB,gBADmBA,IAAAA,ECpKgC,CACnD1D,KAAM,aACNE,MAAM,EACNL,OAAQ,GACRQ,OAAQ,EACRC,MAAO,iBAAM,IACbC,OAAQ,iBAAO,IACfC,QAAS,oIDkKL,YAAY/B,KAAK+E,wBAGSrC,GAS1B1C,KAAK+E,UARK,IAANrC,QAIM,IAANA,EAIiBA,EAHEuC,GAJL,UATDX,kBAC+BW,KA/H7CX,WAAsC,GAgC7BA,SAASrC,EACTqC,QAAQjC,EACRiC,aAAaZ,EACbY,YAAYzB,EACZyB,UAAUhB,EACVgB,WAAWV,EACXU,QAAQR,EAyEjBQ,QAAQ,CACbG,kBAAe7G,EAAUpB,GACvB,OAAiD,IAA1CiC,EAAmBjC,EAAMoB,GAAO,IAEzC8B,gBACEC,EACAnC,EACA0H,GAEA,gBAFAA,IAAAA,GAAY,GAELA,EAAY9E,EAAmBT,EAAMnC,GAAOkC,EAAUC,EAAMnC,SA2BpD2H,iFAAiBH,KE3LtClJ,OAAOyC,eAAe6G,QAAS,aAAc,CAC3CxH,OAAO"}