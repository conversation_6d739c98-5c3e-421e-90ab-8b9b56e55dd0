function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function t(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function n(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function o(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)t.indexOf(r=i[n])>=0||(o[r]=e[r]);return o}function i(e){return 1==(null!=(t=e)&&"object"==typeof t&&!1===Array.isArray(t))&&"[object Object]"===Object.prototype.toString.call(e);var t}var u=Object.prototype,a=u.toString,f=u.hasOwnProperty,c=/^\s*function (\w+)/;function s(e){var t,r=null!==(t=null==e?void 0:e.type)&&void 0!==t?t:e;if(r){var n=r.toString().match(c);return n?n[1]:""}return""}var l=function(e){var t,r;return!1!==i(e)&&"function"==typeof(t=e.constructor)&&!1!==i(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf")},p=function(e){return e},y=p;if("production"!==process.env.NODE_ENV){var v="undefined"!=typeof console;y=v?function(e){console.warn("[VueTypes warn]: "+e)}:p}var d=function(e,t){return f.call(e,t)},h=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},b=Array.isArray||function(e){return"[object Array]"===a.call(e)},O=function(e){return"[object Function]"===a.call(e)},g=function(e){return l(e)&&d(e,"_vueTypes_name")},m=function(e){return l(e)&&(d(e,"type")||["_vueTypes_name","validator","default","required"].some(function(t){return d(e,t)}))};function j(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function _(e,t,r){var n;void 0===r&&(r=!1);var o=!0,i="";n=l(e)?e:{type:e};var u=g(n)?n._vueTypes_name+" - ":"";if(m(n)&&null!==n.type){if(void 0===n.type||!0===n.type)return o;if(!n.required&&void 0===t)return o;b(n.type)?(o=n.type.some(function(e){return!0===_(e,t,!0)}),i=n.type.map(function(e){return s(e)}).join(" or ")):o="Array"===(i=s(n))?b(t):"Object"===i?l(t):"String"===i||"Number"===i||"Boolean"===i||"Function"===i?function(e){if(null==e)return"";var t=e.constructor.toString().match(c);return t?t[1]:""}(t)===i:t instanceof n.type}if(!o){var a=u+'value "'+t+'" should be of type "'+i+'"';return!1===r?(y(a),!1):a}if(d(n,"validator")&&O(n.validator)){var f=y,p=[];if(y=function(e){p.push(e)},o=n.validator(t),y=f,!o){var v=(p.length>1?"* ":"")+p.join("\n* ");return p.length=0,!1===r?(y(v),o):v}}return o}function T(e,t){var r=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get:function(){return this.required=!0,this}},def:{value:function(e){return void 0!==e||this.default?O(e)||!0===_(this,e,!0)?(this.default=b(e)?function(){return[].concat(e)}:l(e)?function(){return Object.assign({},e)}:e,this):(y(this._vueTypes_name+' - invalid default value: "'+e+'"'),this):this}}}),n=r.validator;return O(n)&&(r.validator=j(n,r)),r}function x(e,t){var r=T(e,t);return Object.defineProperty(r,"validate",{value:function(e){return O(this.validator)&&y(this._vueTypes_name+" - calling .validate() will overwrite the current custom validator function. Validator info:\n"+JSON.stringify(this)),this.validator=j(e,this),this}})}function w(e,t,r){var n,i,u=(n=t,i={},Object.getOwnPropertyNames(n).forEach(function(e){i[e]=Object.getOwnPropertyDescriptor(n,e)}),Object.defineProperties({},i));if(u._vueTypes_name=e,!l(r))return u;var a,f,c=r.validator,s=o(r,["validator"]);if(O(c)){var p=u.validator;p&&(p=null!==(f=(a=p).__original)&&void 0!==f?f:a),u.validator=j(p?function(e){return p.call(this,e)&&c.call(this,e)}:c,u)}return Object.assign(u,s)}function k(e){return e.replace(/^(?!\s*$)/gm,"  ")}var P=function(){return x("any",{})},A=function(){return x("function",{type:Function})},E=function(){return x("boolean",{type:Boolean})},N=function(){return x("string",{type:String})},V=function(){return x("number",{type:Number})},q=function(){return x("array",{type:Array})},S=function(){return x("object",{type:Object})},F=function(){return T("integer",{type:Number,validator:function(e){return h(e)}})},D=function(){return T("symbol",{validator:function(e){return"symbol"==typeof e}})};function L(e,t){if(void 0===t&&(t="custom validation failed"),"function"!=typeof e)throw new TypeError("[VueTypes error]: You must provide a function as argument");return T(e.name||"<<anonymous function>>",{validator:function(r){var n=e(r);return n||y(this._vueTypes_name+" - "+t),n}})}function Y(e){if(!b(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");var t='oneOf - value should be one of "'+e.join('", "')+'".',r=e.reduce(function(e,t){if(null!=t){var r=t.constructor;-1===e.indexOf(r)&&e.push(r)}return e},[]);return T("oneOf",{type:r.length>0?r:void 0,validator:function(r){var n=-1!==e.indexOf(r);return n||y(t),n}})}function B(e){if(!b(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");for(var t=!1,r=[],n=0;n<e.length;n+=1){var o=e[n];if(m(o)){if(g(o)&&"oneOf"===o._vueTypes_name){r=r.concat(o.type);continue}if(O(o.validator)&&(t=!0),!0!==o.type&&o.type){r=r.concat(o.type);continue}}r.push(o)}return r=r.filter(function(e,t){return r.indexOf(e)===t}),T("oneOfType",t?{type:r,validator:function(t){var r=[],n=e.some(function(e){var n=_(g(e)&&"oneOf"===e._vueTypes_name?e.type||null:e,t,!0);return"string"==typeof n&&r.push(n),!0===n});return n||y("oneOfType - provided value does not match any of the "+r.length+" passed-in validators:\n"+k(r.join("\n"))),n}}:{type:r})}function M(e){return T("arrayOf",{type:Array,validator:function(t){var r,n=t.every(function(t){return!0===(r=_(e,t,!0))});return n||y("arrayOf - value validation error:\n"+k(r)),n}})}function I(e){return T("instanceOf",{type:e})}function J(e){return T("objectOf",{type:Object,validator:function(t){var r,n=Object.keys(t).every(function(n){return!0===(r=_(e,t[n],!0))});return n||y("objectOf - value validation error:\n"+k(r)),n}})}function R(e){var t=Object.keys(e),r=t.filter(function(t){var r;return!!(null===(r=e[t])||void 0===r?void 0:r.required)}),n=T("shape",{type:Object,validator:function(n){var o=this;if(!l(n))return!1;var i=Object.keys(n);if(r.length>0&&r.some(function(e){return-1===i.indexOf(e)})){var u=r.filter(function(e){return-1===i.indexOf(e)});return y(1===u.length?'shape - required property "'+u[0]+'" is not defined.':'shape - required properties "'+u.join('", "')+'" are not defined.'),!1}return i.every(function(r){if(-1===t.indexOf(r))return!0===o._vueTypes_isLoose||(y('shape - shape definition does not include a "'+r+'" property. Allowed keys: "'+t.join('", "')+'".'),!1);var i=_(e[r],n[r],!0);return"string"==typeof i&&y('shape - "'+r+'" property validation error:\n '+k(i)),!0===i})}});return Object.defineProperty(n,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(n,"loose",{get:function(){return this._vueTypes_isLoose=!0,this}}),n}var $=function(){function e(){}return e.extend=function(e){var t=this;if(b(e))return e.forEach(function(e){return t.extend(e)}),this;var r=e.name,n=e.validate,i=void 0!==n&&n,u=e.getter,a=void 0!==u&&u,f=o(e,["name","validate","getter"]);if(d(this,r))throw new TypeError('[VueTypes error]: Type "'+r+'" already defined');var c,s=f.type;return g(s)?(delete f.type,Object.defineProperty(this,r,a?{get:function(){return w(r,s,f)}}:{value:function(){var e,t=w(r,s,f);return t.validator&&(t.validator=(e=t.validator).bind.apply(e,[t].concat([].slice.call(arguments)))),t}})):(c=a?{get:function(){var e=Object.assign({},f);return i?x(r,e):T(r,e)},enumerable:!0}:{value:function(){var e,t,n=Object.assign({},f);return e=i?x(r,n):T(r,n),n.validator&&(e.validator=(t=n.validator).bind.apply(t,[e].concat([].slice.call(arguments)))),e},enumerable:!0},Object.defineProperty(this,r,c))},t(e,null,[{key:"any",get:function(){return P()}},{key:"func",get:function(){return A().def(this.defaults.func)}},{key:"bool",get:function(){return E().def(this.defaults.bool)}},{key:"string",get:function(){return N().def(this.defaults.string)}},{key:"number",get:function(){return V().def(this.defaults.number)}},{key:"array",get:function(){return q().def(this.defaults.array)}},{key:"object",get:function(){return S().def(this.defaults.object)}},{key:"integer",get:function(){return F().def(this.defaults.integer)}},{key:"symbol",get:function(){return D()}}]),e}();function z(e){var o;return void 0===e&&(e={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(o=function(o){function i(){return o.apply(this,arguments)||this}return n(i,o),t(i,null,[{key:"sensibleDefaults",get:function(){return r({},this.defaults)},set:function(t){this.defaults=!1!==t?r({},!0!==t?t:e):{}}}]),i}($)).defaults=r({},e),o}$.defaults={},$.custom=L,$.oneOf=Y,$.instanceOf=I,$.oneOfType=B,$.arrayOf=M,$.objectOf=J,$.shape=R,$.utils={validate:function(e,t){return!0===_(t,e,!0)},toType:function(e,t,r){return void 0===r&&(r=!1),r?x(e,t):T(e,t)}};var C=function(e){function t(){return e.apply(this,arguments)||this}return n(t,e),t}(z());Object.defineProperty(exports,"__esModule",{value:!0}),exports.any=P,exports.array=q,exports.arrayOf=M,exports.bool=E,exports.createTypes=z,exports.custom=L,exports.default=C,exports.fromType=w,exports.func=A,exports.instanceOf=I,exports.integer=F,exports.number=V,exports.object=S,exports.objectOf=J,exports.oneOf=Y,exports.oneOfType=B,exports.shape=R,exports.string=N,exports.symbol=D,exports.toType=T,exports.toValidableType=x,exports.validateType=_;
//# sourceMappingURL=vue-types.js.map
