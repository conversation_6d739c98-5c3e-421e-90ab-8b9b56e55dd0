from django.db import models
from django.contrib.auth.models import User


class Category(models.Model):
    """资产分类模型"""
    name = models.CharField(max_length=100, verbose_name="分类名称")
    description = models.TextField(blank=True, verbose_name="分类描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "资产分类"
        verbose_name_plural = "资产分类"
        ordering = ['name']

    def __str__(self):
        return self.name


class Location(models.Model):
    """位置模型"""
    name = models.CharField(max_length=100, verbose_name="位置名称")
    address = models.TextField(blank=True, verbose_name="详细地址")
    contact_person = models.CharField(max_length=50, blank=True, verbose_name="联系人")
    contact_phone = models.Char<PERSON>ield(max_length=20, blank=True, verbose_name="联系电话")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "位置"
        verbose_name_plural = "位置"
        ordering = ['name']

    def __str__(self):
        return self.name


class Asset(models.Model):
    """资产模型"""
    STATUS_CHOICES = [
        ('active', '使用中'),
        ('inactive', '闲置'),
        ('maintenance', '维修中'),
        ('scrapped', '报废'),
    ]

    asset_number = models.CharField(max_length=50, unique=True, verbose_name="资产编号")
    name = models.CharField(max_length=200, verbose_name="资产名称")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="分类")
    brand = models.CharField(max_length=100, blank=True, verbose_name="品牌")
    model = models.CharField(max_length=100, blank=True, verbose_name="型号")
    serial_number = models.CharField(max_length=100, blank=True, verbose_name="序列号")
    purchase_date = models.DateField(null=True, blank=True, verbose_name="采购日期")
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="采购价格")
    current_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="当前价值")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="状态")
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="位置")
    responsible_person = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="责任人")
    description = models.TextField(blank=True, verbose_name="描述")
    image = models.ImageField(upload_to='assets/', blank=True, null=True, verbose_name="资产图片")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "资产"
        verbose_name_plural = "资产"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.asset_number} - {self.name}"


class MaintenanceRecord(models.Model):
    """维修记录模型"""
    MAINTENANCE_TYPE_CHOICES = [
        ('repair', '维修'),
        ('maintenance', '保养'),
        ('inspection', '检查'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='maintenance_records', verbose_name="资产")
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPE_CHOICES, verbose_name="维修类型")
    description = models.TextField(verbose_name="维修描述")
    cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="费用")
    maintenance_date = models.DateField(verbose_name="维修日期")
    technician = models.CharField(max_length=100, blank=True, verbose_name="技术员")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="创建人")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "维修记录"
        verbose_name_plural = "维修记录"
        ordering = ['-maintenance_date']

    def __str__(self):
        return f"{self.asset.name} - {self.get_maintenance_type_display()}"


class AssetTransfer(models.Model):
    """资产转移记录模型"""
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='transfer_records', verbose_name="资产")
    from_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, related_name='transfers_from', verbose_name="原位置")
    to_location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, related_name='transfers_to', verbose_name="目标位置")
    from_person = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='transfers_from_person', verbose_name="原责任人")
    to_person = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='transfers_to_person', verbose_name="新责任人")
    transfer_date = models.DateField(verbose_name="转移日期")
    reason = models.TextField(blank=True, verbose_name="转移原因")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="创建人")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "资产转移记录"
        verbose_name_plural = "资产转移记录"
        ordering = ['-transfer_date']

    def __str__(self):
        return f"{self.asset.name} - {self.transfer_date}"
