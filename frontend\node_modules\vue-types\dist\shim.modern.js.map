{"version": 3, "file": "shim.modern.js", "sources": ["../node_modules/is-plain-object/index.es.js", "../src/shim.ts", "../src/sensibles.ts"], "sourcesContent": ["/*!\n * isobject <https://github.com/jonschlinkert/isobject>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObject(val) {\n  return val != null && typeof val === 'object' && Array.isArray(val) === false;\n}\n\n/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObjectObject(o) {\n  return isObject(o) === true\n    && Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObjectObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (typeof ctor !== 'function') return false;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObjectObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n}\n\nexport default isPlainObject;\n", "import isPlainObject from 'is-plain-object'\nimport { typeDefaults } from './sensibles'\nimport { VueTypesDefaults } from './types'\nexport { VueTypeDef, VueTypeValidableDef } from './types'\nconst dfn = Object.defineProperty\n\nconst isArray =\n  Array.isArray ||\n  function (value) {\n    return Object.prototype.toString.call(value) === '[object Array]'\n  }\n\nfunction type<T = any>(name: string, props: any = {}, validable = false): T {\n  const descriptors: PropertyDescriptorMap = {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    def: {\n      value(v) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const t = this\n        if (v === undefined && !t.default) {\n          return t\n        }\n        if (isArray(v)) {\n          t.default = () => [].concat(v)\n        } else if (isPlainObject(v)) {\n          t.default = () => Object.assign({}, v)\n        } else {\n          t.default = v\n        }\n        return t\n      },\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n  }\n\n  if (validable) {\n    ;(descriptors as any).validate = {\n      // eslint-disable-next-line @typescript-eslint/no-empty-function\n      value() {},\n    }\n  }\n  return Object.assign(\n    Object.defineProperties(\n      {\n        validator: () => true,\n      },\n      descriptors,\n    ),\n    props,\n  )\n}\n\nexport const any = () => type('any', {}, true)\nexport const func = <T = any>() => type<T>('func', { type: Function }, true)\nexport const bool = () => type('bool', { type: Boolean }, true)\nexport const string = () => type('string', { type: String }, true)\nexport const number = () => type('number', { type: Number }, true)\nexport const array = <T = any>() => type<T>('array', { type: Array }, true)\nexport const object = <T = any>() => type<T>('object', { type: Object }, true)\nexport const symbol = () => type('symbol')\nexport const integer = () => type('integer', { type: Number })\n/* eslint-disable @typescript-eslint/no-unused-vars */\nexport const oneOf = <T = any>(a: any) => type<T>('oneOf')\nexport const custom = <T = any>(a: any) => type<T>('custom')\nexport const instanceOf = <T = any>(Constr: any) =>\n  type<T>('instanceOf', { type: Constr })\nexport const oneOfType = <T = any>(a: any) => type<T>('oneOfType')\nexport const arrayOf = <T = any>(a: any) => type<T>('arrayOf', { type: Array })\n\nexport const objectOf = <T = any>(a: any) =>\n  type<T>('objectOf', { type: Object })\nexport const shape = <T = any>(a: any) =>\n  dfn(\n    type<T>('shape', { type: Object }),\n    'loose',\n    {\n      get() {\n        return this\n      },\n    },\n  )\n/* eslint-enable @typescript-eslint/no-unused-vars */\n\nfunction createValidator(\n  root: any,\n  name: string,\n  props: any,\n  getter = false,\n  validable = false,\n) {\n  const prop = getter ? 'get' : 'value'\n  const descr = {\n    [prop]: () =>\n      type(name, props, validable).def(\n        getter ? root.defaults[name] : undefined,\n      ),\n  }\n\n  return dfn(root, name, descr)\n}\n\nclass BaseVueTypes {\n  static defaults: Partial<VueTypesDefaults> = {}\n\n  static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n  static get any() {\n    return any()\n  }\n  static get func() {\n    return func().def(this.defaults.func)\n  }\n  static get bool() {\n    return bool().def(this.defaults.bool)\n  }\n  static get string() {\n    return string().def(this.defaults.string)\n  }\n  static get number() {\n    return number().def(this.defaults.number)\n  }\n  static get array() {\n    return array().def(this.defaults.array)\n  }\n  static get object() {\n    return object().def(this.defaults.object)\n  }\n  static get symbol() {\n    return symbol()\n  }\n  static get integer() {\n    return integer().def(this.defaults.integer)\n  }\n  static oneOf = oneOf\n  static custom = custom\n  static instanceOf = instanceOf\n  static oneOfType = oneOfType\n  static arrayOf = arrayOf\n  static objectOf = objectOf\n  static shape = shape\n  static extend<T = any>(props): T {\n    const { name, validate, getter = false, type = null } = props\n    // If we are inheriting from a custom type, let's ignore the type property\n    const extType = isPlainObject(type) && type.type ? null : type\n    return createValidator(this, name, { type: extType }, getter, !!validate)\n  }\n  static utils = {\n    toType: type as (...args: any[]) => any,\n    validate: (...args: any[]) => !!args,\n  }\n}\n\nexport function createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\n/* eslint-disable no-console */\nif (process.env.NODE_ENV !== 'production') {\n  console.warn(\n    'You are using the production shimmed version of VueTypes in a development build. Refer to https://github.com/dwightjack/vue-types#production-build to learn how to configure VueTypes for usage in multiple environments.',\n  )\n}\n/* eslint-enable no-console */\n\nexport default class VueTypes extends createTypes() {}\n", "import { VueTypesDefaults } from './types'\n\nexport const typeDefaults = (): VueTypesDefaults => ({\n  func: () => undefined,\n  bool: true,\n  string: '',\n  number: 0,\n  array: () => [],\n  object: () => ({}),\n  integer: 0,\n})\n"], "names": ["isObjectObject", "o", "val", "Array", "isArray", "Object", "prototype", "toString", "call", "isPlainObject", "ctor", "prot", "constructor", "hasOwnProperty", "dfn", "defineProperty", "value", "type", "name", "props", "validable", "descriptors", "_vueTypes_name", "writable", "def", "v", "t", "this", "undefined", "default", "concat", "assign", "isRequired", "get", "required", "validate", "defineProperties", "validator", "any", "func", "Function", "bool", "Boolean", "string", "String", "number", "Number", "array", "object", "symbol", "integer", "oneOf", "a", "custom", "instanceOf", "Constr", "oneOfType", "arrayOf", "objectOf", "shape", "createValidator", "root", "getter", "[object Object]", "defaults", "BaseVueTypes", "createTypes", "defs", "sensibleDefault<PERSON>", "toType", "args", "process", "env", "NODE_ENV", "console", "warn", "VueTypes"], "mappings": "AAkBA,SAASA,EAAeC,GACtB,OAAuB,IAXT,OADEC,EAYAD,IAXqB,iBAARC,IAA2C,IAAvBC,MAAMC,QAAQF,KAYpB,oBAAtCG,OAAOC,UAAUC,SAASC,KAAKP,GAbtC,IAAkBC,EAgBlB,SAASO,EAAcR,GACrB,IAAIS,EAAKC,EAET,OAA0B,IAAtBX,EAAeC,IAIC,mBADpBS,EAAOT,EAAEW,eAKoB,IAAzBZ,EADJW,EAAOD,EAAKJ,aAIiC,IAAzCK,EAAKE,eAAe,uBCjCpBC,EAAMT,OAAOU,eAEbX,EACJD,MAAMC,SACN,SAAUY,GACR,MAAiD,mBAA1CX,OAAOC,UAAUC,SAASC,KAAKQ,IAG1C,SAASC,EAAcC,EAAcC,EAAa,GAAIC,GAAY,GAChE,MAAMC,EAAqC,CACzCC,eAAgB,CACdN,MAAOE,EACPK,UAAU,GAEZC,IAAK,CACHR,MAAMS,GAEJ,MAAMC,EAAIC,KACV,YAAUC,IAANH,GAAoBC,EAAEG,SAIxBH,EAAEG,QADAzB,EAAQqB,GACE,IAAM,GAAGK,OAAOL,GACnBhB,EAAcgB,GACX,IAAMpB,OAAO0B,OAAO,GAAIN,GAExBA,EAEPC,GATEA,IAYbM,WAAY,CACVC,MAEE,OADAN,KAAKO,UAAW,UAYtB,OANId,IACFC,EAAsBc,SAAW,CAE/BnB,YAGGX,OAAO0B,OACZ1B,OAAO+B,iBACL,CACEC,UAAW,KAAM,GAEnBhB,GAEFF,GAIJ,MAAamB,EAAM,IAAMrB,EAAK,MAAO,IAAI,GAC5BsB,EAAO,IAAetB,EAAQ,OAAQ,CAAEA,KAAMuB,WAAY,GAC1DC,EAAO,IAAMxB,EAAK,OAAQ,CAAEA,KAAMyB,UAAW,GAC7CC,EAAS,IAAM1B,EAAK,SAAU,CAAEA,KAAM2B,SAAU,GAChDC,EAAS,IAAM5B,EAAK,SAAU,CAAEA,KAAM6B,SAAU,GAChDC,EAAQ,IAAe9B,EAAQ,QAAS,CAAEA,KAAMd,QAAS,GACzD6C,EAAS,IAAe/B,EAAQ,SAAU,CAAEA,KAAMZ,SAAU,GAC5D4C,EAAS,IAAMhC,EAAK,UACpBiC,EAAU,IAAMjC,EAAK,UAAW,CAAEA,KAAM6B,SAExCK,EAAkBC,GAAWnC,EAAQ,SACrCoC,EAAmBD,GAAWnC,EAAQ,UACtCqC,EAAuBC,GAClCtC,EAAQ,aAAc,CAAEA,KAAMsC,IACnBC,EAAsBJ,GAAWnC,EAAQ,aACzCwC,EAAoBL,GAAWnC,EAAQ,UAAW,CAAEA,KAAMd,QAE1DuD,EAAqBN,GAChCnC,EAAQ,WAAY,CAAEA,KAAMZ,SACjBsD,EAAkBP,GAC7BtC,EACEG,EAAQ,QAAS,CAAEA,KAAMZ,SACzB,QACA,CACE4B,MACE,eAMR,SAAS2B,EACPC,EACA3C,EACAC,EACA2C,GAAS,EACT1C,GAAY,GAUZ,OAAON,EAAI+C,EAAM3C,EAPH,CACZ6C,CAFWD,EAAS,MAAQ,SAEpB,IACN7C,EAAKC,EAAMC,EAAOC,GAAWI,IAC3BsC,EAASD,EAAKG,SAAS9C,QAAQU,KAOvC,MAAMqC,EAKJ3B,iBACE,OAAOA,IAETC,kBACE,OAAOA,IAAOf,IAAIG,KAAKqC,SAASzB,MAElCE,kBACE,OAAOA,IAAOjB,IAAIG,KAAKqC,SAASvB,MAElCE,oBACE,OAAOA,IAASnB,IAAIG,KAAKqC,SAASrB,QAEpCE,oBACE,OAAOA,IAASrB,IAAIG,KAAKqC,SAASnB,QAEpCE,mBACE,OAAOA,IAAQvB,IAAIG,KAAKqC,SAASjB,OAEnCC,oBACE,OAAOA,IAASxB,IAAIG,KAAKqC,SAAShB,QAEpCC,oBACE,OAAOA,IAETC,qBACE,OAAOA,IAAU1B,IAAIG,KAAKqC,SAASd,SASrCa,cAAuB5C,GACrB,MAAMD,KAAEA,EAAFiB,SAAQA,EAAR2B,OAAkBA,GAAS,EAA3B7C,KAAkCA,EAAO,MAASE,EAGxD,OAAOyC,EAAgBjC,KAAMT,EAAM,CAAED,KADrBR,EAAcQ,IAASA,EAAKA,KAAO,KAAOA,GACJ6C,IAAU3B,aAQpD+B,EAAYC,GC7J1B5B,KAAM,OACNE,MAAM,EACNE,OAAQ,GACRE,OAAQ,EACRE,MAAO,IAAM,GACbC,OAAQ,SACRE,QAAS,UDwJT,SAAO,cAAce,EAGnBG,8BACE,MAAO,IAAKzC,KAAKqC,UAGnBI,4BAA4B3C,GAS1BE,KAAKqC,UARK,IAANvC,GAIM,IAANA,EAIY,IAAKA,GAHH,IAAK0C,GAJL,eARF,IAAKA,KApDlBF,WAAsC,GA+BtCA,QAAQd,EACRc,SAASZ,EACTY,aAAaX,EACbW,YAAYT,EACZS,UAAUR,EACVQ,WAAWP,EACXO,QAAQN,EAORM,QAAQ,CACbI,OAAQpD,EACRkB,SAAU,IAAImC,MAAkBA,GA2BP,eAAzBC,QAAQC,IAAIC,UACdC,QAAQC,KACN,mOAKiBC,UAAiBV"}