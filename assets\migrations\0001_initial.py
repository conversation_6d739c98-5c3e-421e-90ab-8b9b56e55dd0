# Generated by Django 4.2.7 on 2025-08-21 07:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "asset_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="资产编号"
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="资产名称")),
                (
                    "brand",
                    models.CharField(blank=True, max_length=100, verbose_name="品牌"),
                ),
                (
                    "model",
                    models.CharField(blank=True, max_length=100, verbose_name="型号"),
                ),
                (
                    "serial_number",
                    models.Char<PERSON><PERSON>(blank=True, max_length=100, verbose_name="序列号"),
                ),
                (
                    "purchase_date",
                    models.DateField(blank=True, null=True, verbose_name="采购日期"),
                ),
                (
                    "purchase_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="采购价格",
                    ),
                ),
                (
                    "current_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="当前价值",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "使用中"),
                            ("inactive", "闲置"),
                            ("maintenance", "维修中"),
                            ("scrapped", "报废"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="描述")),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="assets/",
                        verbose_name="资产图片",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "资产",
                "verbose_name_plural": "资产",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="分类名称")),
                ("description", models.TextField(blank=True, verbose_name="分类描述")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "资产分类",
                "verbose_name_plural": "资产分类",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Location",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="位置名称")),
                ("address", models.TextField(blank=True, verbose_name="详细地址")),
                (
                    "contact_person",
                    models.CharField(blank=True, max_length=50, verbose_name="联系人"),
                ),
                (
                    "contact_phone",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="联系电话"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "位置",
                "verbose_name_plural": "位置",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="MaintenanceRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "maintenance_type",
                    models.CharField(
                        choices=[
                            ("repair", "维修"),
                            ("maintenance", "保养"),
                            ("inspection", "检查"),
                        ],
                        max_length=20,
                        verbose_name="维修类型",
                    ),
                ),
                ("description", models.TextField(verbose_name="维修描述")),
                (
                    "cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="费用",
                    ),
                ),
                ("maintenance_date", models.DateField(verbose_name="维修日期")),
                (
                    "technician",
                    models.CharField(blank=True, max_length=100, verbose_name="技术员"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="maintenance_records",
                        to="assets.asset",
                        verbose_name="资产",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
            ],
            options={
                "verbose_name": "维修记录",
                "verbose_name_plural": "维修记录",
                "ordering": ["-maintenance_date"],
            },
        ),
        migrations.CreateModel(
            name="AssetTransfer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("transfer_date", models.DateField(verbose_name="转移日期")),
                ("reason", models.TextField(blank=True, verbose_name="转移原因")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfer_records",
                        to="assets.asset",
                        verbose_name="资产",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
                (
                    "from_location",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transfers_from",
                        to="assets.location",
                        verbose_name="原位置",
                    ),
                ),
                (
                    "from_person",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transfers_from_person",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="原责任人",
                    ),
                ),
                (
                    "to_location",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transfers_to",
                        to="assets.location",
                        verbose_name="目标位置",
                    ),
                ),
                (
                    "to_person",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transfers_to_person",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="新责任人",
                    ),
                ),
            ],
            options={
                "verbose_name": "资产转移记录",
                "verbose_name_plural": "资产转移记录",
                "ordering": ["-transfer_date"],
            },
        ),
        migrations.AddField(
            model_name="asset",
            name="category",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="assets.category",
                verbose_name="分类",
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="location",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="assets.location",
                verbose_name="位置",
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="responsible_person",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
                verbose_name="责任人",
            ),
        ),
    ]
