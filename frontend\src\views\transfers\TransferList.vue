<template>
  <div>
    <!-- 搜索和操作栏 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索资产名称、编号"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        
        <a-col :span="4">
          <a-select
            v-model:value="filters.from_location"
            placeholder="原位置"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="location in locations"
              :key="location.id"
              :value="location.id"
            >
              {{ location.name }}
            </a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="4">
          <a-select
            v-model:value="filters.to_location"
            placeholder="目标位置"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="location in locations"
              :key="location.id"
              :value="location.id"
            >
              {{ location.name }}
            </a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="6">
          <a-range-picker
            v-model:value="dateRange"
            style="width: 100%"
            @change="handleDateChange"
            placeholder="选择转移日期范围"
          />
        </a-col>
        
        <a-col :span="4" style="text-align: right;">
          <a-button @click="resetFilters">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 转移记录表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="transferRecords"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'asset_info'">
            <div>
              <div style="font-weight: bold;">{{ record.asset_name }}</div>
              <div style="color: #666; font-size: 12px;">{{ record.asset_number }}</div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'location_transfer'">
            <div>
              <a-tag color="blue">{{ record.from_location_name || '未知' }}</a-tag>
              <ArrowRightOutlined style="margin: 0 8px;" />
              <a-tag color="green">{{ record.to_location_name || '未知' }}</a-tag>
            </div>
          </template>
          
          <template v-else-if="column.key === 'person_transfer'">
            <div>
              <span>{{ record.from_person_name || '-' }}</span>
              <ArrowRightOutlined style="margin: 0 8px;" />
              <span>{{ record.to_person_name || '-' }}</span>
            </div>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看详情
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 转移详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="转移记录详情"
      :footer="null"
      width="600px"
    >
      <div v-if="currentRecord">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="资产信息">
            <div>
              <div style="font-weight: bold;">{{ currentRecord.asset_name }}</div>
              <div style="color: #666;">编号: {{ currentRecord.asset_number }}</div>
            </div>
          </a-descriptions-item>
          
          <a-descriptions-item label="位置转移">
            <div>
              <a-tag color="blue">{{ currentRecord.from_location_name || '未设置' }}</a-tag>
              <ArrowRightOutlined style="margin: 0 8px;" />
              <a-tag color="green">{{ currentRecord.to_location_name || '未设置' }}</a-tag>
            </div>
          </a-descriptions-item>
          
          <a-descriptions-item label="责任人转移">
            <div>
              <span>{{ currentRecord.from_person_name || '未设置' }}</span>
              <ArrowRightOutlined style="margin: 0 8px;" />
              <span>{{ currentRecord.to_person_name || '未设置' }}</span>
            </div>
          </a-descriptions-item>
          
          <a-descriptions-item label="转移日期">
            {{ currentRecord.transfer_date }}
          </a-descriptions-item>
          
          <a-descriptions-item label="转移原因">
            {{ currentRecord.reason || '无' }}
          </a-descriptions-item>
          
          <a-descriptions-item label="操作人">
            {{ currentRecord.created_by_name }}
          </a-descriptions-item>
          
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(currentRecord.created_at) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { transferAPI, locationAPI } from '../../services/api'
import { ArrowRightOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const transferRecords = ref([])
const locations = ref([])
const loading = ref(false)
const searchText = ref('')
const dateRange = ref([])
const filters = ref({
  from_location: undefined,
  to_location: undefined
})

const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

const detailModalVisible = ref(false)
const currentRecord = ref(null)

const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

const columns = [
  {
    title: '资产信息',
    key: 'asset_info',
    width: 200
  },
  {
    title: '位置转移',
    key: 'location_transfer',
    width: 250
  },
  {
    title: '责任人转移',
    key: 'person_transfer',
    width: 200
  },
  {
    title: '转移日期',
    dataIndex: 'transfer_date',
    key: 'transfer_date',
    sorter: true
  },
  {
    title: '转移原因',
    dataIndex: 'reason',
    key: 'reason',
    ellipsis: true
  },
  {
    title: '操作人',
    dataIndex: 'created_by_name',
    key: 'created_by_name'
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
]

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const fetchTransferRecords = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.value.current,
      page_size: pagination.value.pageSize,
      ...filters.value
    }
    
    if (searchText.value) {
      params.search = searchText.value
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.transfer_date_after = dateRange.value[0].format('YYYY-MM-DD')
      params.transfer_date_before = dateRange.value[1].format('YYYY-MM-DD')
    }
    
    const response = await transferAPI.getTransferRecords(params)
    transferRecords.value = response.results || []
    pagination.value.total = response.count || 0
  } catch (error) {
    message.error('获取转移记录失败')
  } finally {
    loading.value = false
  }
}

const fetchLocations = async () => {
  try {
    const response = await locationAPI.getLocations({ page_size: 1000 })
    locations.value = response.results || []
  } catch (error) {
    console.error('获取位置列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.value.current = 1
  fetchTransferRecords()
}

const handleFilterChange = () => {
  pagination.value.current = 1
  fetchTransferRecords()
}

const handleDateChange = () => {
  pagination.value.current = 1
  fetchTransferRecords()
}

const resetFilters = () => {
  searchText.value = ''
  dateRange.value = []
  filters.value = {
    from_location: undefined,
    to_location: undefined
  }
  pagination.value.current = 1
  fetchTransferRecords()
}

const handleTableChange = (pag, tableFilters, sorter) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchTransferRecords()
}

const viewDetail = (record) => {
  currentRecord.value = record
  detailModalVisible.value = true
}

onMounted(() => {
  fetchTransferRecords()
  fetchLocations()
})
</script>
