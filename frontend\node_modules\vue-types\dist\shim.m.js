function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function e(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}function n(){return(n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function r(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function u(t){return 1==(null!=(e=t)&&"object"==typeof e&&!1===Array.isArray(e))&&"[object Object]"===Object.prototype.toString.call(t);var e}function o(t){var e,n;return!1!==u(t)&&"function"==typeof(e=t.constructor)&&!1!==u(n=e.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")}var i=Object.defineProperty,c=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function f(t,e,n){void 0===e&&(e={}),void 0===n&&(n=!1);var r={_vueTypes_name:{value:t,writable:!0},def:{value:function(t){var e=this;return void 0!==t||e.default?(e.default=c(t)?function(){return[].concat(t)}:o(t)?function(){return Object.assign({},t)}:t,e):e}},isRequired:{get:function(){return this.required=!0,this}}};return n&&(r.validate={value:function(){}}),Object.assign(Object.defineProperties({validator:function(){return!0}},r),e)}var a=function(){return f("any",{},!0)},s=function(){return f("func",{type:Function},!0)},l=function(){return f("bool",{type:Boolean},!0)},y=function(){return f("string",{type:String},!0)},p=function(){return f("number",{type:Number},!0)},d=function(){return f("array",{type:Array},!0)},b=function(){return f("object",{type:Object},!0)},v=function(){return f("symbol")},g=function(){return f("integer",{type:Number})},h=function(t){return f("oneOf")},O=function(t){return f("custom")},j=function(t){return f("instanceOf",{type:t})},m=function(t){return f("oneOfType")},k=function(t){return f("arrayOf",{type:Array})},w=function(t){return f("objectOf",{type:Object})},A=function(t){return i(f("shape",{type:Object}),"loose",{get:function(){return this}})};function _(t,e,n,r,u){var o;void 0===r&&(r=!1),void 0===u&&(u=!1);var c=((o={})[r?"get":"value"]=function(){return f(e,n,u).def(r?t.defaults[e]:void 0)},o);return i(t,e,c)}var P=function(){function t(){}return t.extend=function(t){var e=t.validate,n=t.getter,r=void 0!==n&&n,u=t.type,i=void 0===u?null:u;return _(this,t.name,{type:o(i)&&i.type?null:i},r,!!e)},e(t,null,[{key:"any",get:function(){return a()}},{key:"func",get:function(){return s().def(this.defaults.func)}},{key:"bool",get:function(){return l().def(this.defaults.bool)}},{key:"string",get:function(){return y().def(this.defaults.string)}},{key:"number",get:function(){return p().def(this.defaults.number)}},{key:"array",get:function(){return d().def(this.defaults.array)}},{key:"object",get:function(){return b().def(this.defaults.object)}},{key:"symbol",get:function(){return v()}},{key:"integer",get:function(){return g().def(this.defaults.integer)}}]),t}();function T(t){var u;return void 0===t&&(t={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(u=function(u){function o(){return u.apply(this,arguments)||this}return r(o,u),e(o,null,[{key:"sensibleDefaults",get:function(){return n({},this.defaults)},set:function(e){this.defaults=!1!==e?n({},!0!==e?e:t):{}}}]),o}(P)).defaults=n({},t),u}P.defaults={},P.oneOf=h,P.custom=O,P.instanceOf=j,P.oneOfType=m,P.arrayOf=k,P.objectOf=w,P.shape=A,P.utils={toType:f,validate:function(){return!![].slice.call(arguments)}},"production"!==process.env.NODE_ENV&&console.warn("You are using the production shimmed version of VueTypes in a development build. Refer to https://github.com/dwightjack/vue-types#production-build to learn how to configure VueTypes for usage in multiple environments.");var N=function(t){function e(){return t.apply(this,arguments)||this}return r(e,t),e}(T());export default N;export{a as any,d as array,k as arrayOf,l as bool,T as createTypes,O as custom,s as func,j as instanceOf,g as integer,p as number,b as object,w as objectOf,h as oneOf,m as oneOfType,A as shape,y as string,v as symbol};
//# sourceMappingURL=shim.m.js.map
