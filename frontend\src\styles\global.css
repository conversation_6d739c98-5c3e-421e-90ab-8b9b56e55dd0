:root {
  --bg: #f6f8fa;
  --card-bg: #ffffff;
  --primary: #1677ff;
  --success: #52c41a;
  --warning: #faad14;
  --danger: #ff4d4f;
  --text: #1f1f1f;
  --muted: #8c8c8c;
  --radius: 10px;
  --shadow: 0 6px 24px rgba(0, 0, 0, 0.06);
  --space-8: 8px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;
}

html, body, #app {
  height: 100%;
}

body {
  background: var(--bg);
  color: var(--text);
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Utilities */
.mb-16 { margin-bottom: var(--space-16); }
.mb-20 { margin-bottom: var(--space-20); }
.mb-24 { margin-bottom: var(--space-24); }

.center { text-align: center; }
.p-20 { padding: var(--space-20); }

/* Card polish */
.ant-card {
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}
.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}
.ant-card-head-title {
  font-weight: 600;
}

/* Header polish */
.top-header {
  background: #fff !important;
  box-shadow: 0 2px 12px rgba(0,0,0,0.05) !important;
}

/* Statistic size */
.ant-statistic-title {
  color: var(--muted);
}
.ant-statistic-content {
  font-weight: 700;
}

/* Charts */
.chart { height: 300px; }

/* Container */
.container { max-width: 1200px; margin: 0 auto; }
