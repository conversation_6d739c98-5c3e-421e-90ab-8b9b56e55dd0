from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Category, Location, Asset, MaintenanceRecord, AssetTransfer


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'


class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = '__all__'


class AssetSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    responsible_person_name = serializers.CharField(source='responsible_person.username', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = Asset
        fields = '__all__'

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # 添加关联对象的详细信息
        if instance.category:
            data['category_detail'] = CategorySerializer(instance.category).data
        if instance.location:
            data['location_detail'] = LocationSerializer(instance.location).data
        if instance.responsible_person:
            data['responsible_person_detail'] = UserSerializer(instance.responsible_person).data
        return data


class MaintenanceRecordSerializer(serializers.ModelSerializer):
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    asset_number = serializers.CharField(source='asset.asset_number', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    maintenance_type_display = serializers.CharField(source='get_maintenance_type_display', read_only=True)

    class Meta:
        model = MaintenanceRecord
        fields = '__all__'


class AssetTransferSerializer(serializers.ModelSerializer):
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    asset_number = serializers.CharField(source='asset.asset_number', read_only=True)
    from_location_name = serializers.CharField(source='from_location.name', read_only=True)
    to_location_name = serializers.CharField(source='to_location.name', read_only=True)
    from_person_name = serializers.CharField(source='from_person.username', read_only=True)
    to_person_name = serializers.CharField(source='to_person.username', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = AssetTransfer
        fields = '__all__'


class AssetSummarySerializer(serializers.Serializer):
    """资产统计摘要序列化器"""
    total_assets = serializers.IntegerField()
    active_assets = serializers.IntegerField()
    inactive_assets = serializers.IntegerField()
    maintenance_assets = serializers.IntegerField()
    scrapped_assets = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    categories_count = serializers.IntegerField()
    locations_count = serializers.IntegerField()
