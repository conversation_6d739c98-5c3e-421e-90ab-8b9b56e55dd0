<template>
  <div>
    <!-- 操作栏 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索位置名称、地址"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        
        <a-col :span="16" style="text-align: right;">
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined />
            添加位置
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 位置表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="locations"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个位置吗？"
                @confirm="deleteLocation(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑位置模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑位置' : '添加位置'"
      @ok="handleSubmit"
      @cancel="resetForm"
      width="600px"
    >
      <a-form :model="form" layout="vertical" ref="formRef">
        <a-form-item
          label="位置名称"
          name="name"
          :rules="[{ required: true, message: '请输入位置名称' }]"
        >
          <a-input v-model:value="form.name" placeholder="请输入位置名称" />
        </a-form-item>
        
        <a-form-item label="详细地址" name="address">
          <a-textarea
            v-model:value="form.address"
            placeholder="请输入详细地址"
            :rows="3"
          />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="联系人" name="contact_person">
              <a-input v-model:value="form.contact_person" placeholder="请输入联系人" />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="联系电话" name="contact_phone">
              <a-input v-model:value="form.contact_phone" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { locationAPI } from '../../services/api'
import { PlusOutlined } from '@ant-design/icons-vue'

const locations = ref([])
const loading = ref(false)
const searchText = ref('')
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const form = ref({
  id: null,
  name: '',
  address: '',
  contact_person: '',
  contact_phone: ''
})

const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

const columns = [
  {
    title: '位置名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    key: 'address'
  },
  {
    title: '联系人',
    dataIndex: 'contact_person',
    key: 'contact_person'
  },
  {
    title: '联系电话',
    dataIndex: 'contact_phone',
    key: 'contact_phone'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

const fetchLocations = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.value.current,
      page_size: pagination.value.pageSize
    }
    
    if (searchText.value) {
      params.search = searchText.value
    }
    
    const response = await locationAPI.getLocations(params)
    locations.value = response.results || []
    pagination.value.total = response.count || 0
  } catch (error) {
    message.error('获取位置列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.value.current = 1
  fetchLocations()
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchLocations()
}

const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

const showEditModal = (record) => {
  isEdit.value = true
  form.value = { ...record }
  modalVisible.value = true
}

const resetForm = () => {
  form.value = {
    id: null,
    name: '',
    address: '',
    contact_person: '',
    contact_phone: ''
  }
  modalVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await locationAPI.updateLocation(form.value.id, form.value)
      message.success('更新成功')
    } else {
      await locationAPI.createLocation(form.value)
      message.success('创建成功')
    }
    
    resetForm()
    fetchLocations()
  } catch (error) {
    if (error.errorFields) {
      return // 表单验证错误
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const deleteLocation = async (id) => {
  try {
    await locationAPI.deleteLocation(id)
    message.success('删除成功')
    fetchLocations()
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  fetchLocations()
})
</script>
