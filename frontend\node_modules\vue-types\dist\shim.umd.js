!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).VueTypes={})}(this,function(t){function e(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function n(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}function r(){return(r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function u(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function o(t){return 1==(null!=(e=t)&&"object"==typeof e&&!1===Array.isArray(e))&&"[object Object]"===Object.prototype.toString.call(t);var e}function i(t){var e,n;return!1!==o(t)&&"function"==typeof(e=t.constructor)&&!1!==o(n=e.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")}var f=Object.defineProperty,c=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function a(t,e,n){void 0===e&&(e={}),void 0===n&&(n=!1);var r={_vueTypes_name:{value:t,writable:!0},def:{value:function(t){var e=this;return void 0!==t||e.default?(e.default=c(t)?function(){return[].concat(t)}:i(t)?function(){return Object.assign({},t)}:t,e):e}},isRequired:{get:function(){return this.required=!0,this}}};return n&&(r.validate={value:function(){}}),Object.assign(Object.defineProperties({validator:function(){return!0}},r),e)}var s=function(){return a("any",{},!0)},y=function(){return a("func",{type:Function},!0)},l=function(){return a("bool",{type:Boolean},!0)},p=function(){return a("string",{type:String},!0)},d=function(){return a("number",{type:Number},!0)},b=function(){return a("array",{type:Array},!0)},v=function(){return a("object",{type:Object},!0)},g=function(){return a("symbol")},O=function(){return a("integer",{type:Number})},h=function(t){return a("oneOf")},j=function(t){return a("custom")},m=function(t){return a("instanceOf",{type:t})},k=function(t){return a("oneOfType")},A=function(t){return a("arrayOf",{type:Array})},T=function(t){return a("objectOf",{type:Object})},P=function(t){return f(a("shape",{type:Object}),"loose",{get:function(){return this}})};function _(t,e,n,r,u){var o;void 0===r&&(r=!1),void 0===u&&(u=!1);var i=((o={})[r?"get":"value"]=function(){return a(e,n,u).def(r?t.defaults[e]:void 0)},o);return f(t,e,i)}var w=function(){function t(){}return t.extend=function(t){var e=t.validate,n=t.getter,r=void 0!==n&&n,u=t.type,o=void 0===u?null:u;return _(this,t.name,{type:i(o)&&o.type?null:o},r,!!e)},n(t,null,[{key:"any",get:function(){return s()}},{key:"func",get:function(){return y().def(this.defaults.func)}},{key:"bool",get:function(){return l().def(this.defaults.bool)}},{key:"string",get:function(){return p().def(this.defaults.string)}},{key:"number",get:function(){return d().def(this.defaults.number)}},{key:"array",get:function(){return b().def(this.defaults.array)}},{key:"object",get:function(){return v().def(this.defaults.object)}},{key:"symbol",get:function(){return g()}},{key:"integer",get:function(){return O().def(this.defaults.integer)}}]),t}();function x(t){var e;return void 0===t&&(t={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(e=function(e){function o(){return e.apply(this,arguments)||this}return u(o,e),n(o,null,[{key:"sensibleDefaults",get:function(){return r({},this.defaults)},set:function(e){this.defaults=!1!==e?r({},!0!==e?e:t):{}}}]),o}(w)).defaults=r({},t),e}w.defaults={},w.oneOf=h,w.custom=j,w.instanceOf=m,w.oneOfType=k,w.arrayOf=A,w.objectOf=T,w.shape=P,w.utils={toType:a,validate:function(){return!![].slice.call(arguments)}};var S=function(t){function e(){return t.apply(this,arguments)||this}return u(e,t),e}(x());t.any=s,t.array=b,t.arrayOf=A,t.bool=l,t.createTypes=x,t.custom=j,t.default=S,t.func=y,t.instanceOf=m,t.integer=O,t.number=d,t.object=v,t.objectOf=T,t.oneOf=h,t.oneOfType=k,t.shape=P,t.string=p,t.symbol=g});
//# sourceMappingURL=shim.umd.js.map
