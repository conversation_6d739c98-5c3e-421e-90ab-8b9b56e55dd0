/* 通用页面样式 */

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  gap: 16px;
}

.header-actions .ant-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.header-actions .ant-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.header-actions .ant-btn-primary {
  background: rgba(255, 255, 255, 0.9);
  color: #1890ff;
}

.header-actions .ant-btn-primary:hover {
  background: white;
  color: #096dd9;
}

/* 概览卡片样式 */
.overview-section {
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #096dd9);
}

.overview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.overview-card.card-1::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.overview-card.card-2::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.overview-card.card-3::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.overview-card.card-4::before {
  background: linear-gradient(90deg, #f5222d, #ff4d4f);
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.overview-card.card-1 .card-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.overview-card.card-2 .card-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.overview-card.card-3 .card-icon {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.overview-card.card-4 .card-icon {
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4px;
  line-height: 1;
}

.card-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-trend.up {
  color: #52c41a;
}

.card-trend.down {
  color: #ff4d4f;
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 4px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #1890ff;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #1890ff;
}

.header-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 通用卡片样式 */
.common-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.common-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.common-card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.common-card-content {
  padding: 24px;
}

/* 模块卡片样式 */
.module-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.module-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.module-status.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.module-status.warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.module-status.maintenance {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.module-content {
  flex: 1;
  margin-bottom: 16px;
}

.module-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.module-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.module-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.module-actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .overview-card {
    height: auto;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .overview-card {
    padding: 20px;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .card-value {
    font-size: 20px;
  }
  
  .common-card-content {
    padding: 16px;
  }
  
  .module-card {
    padding: 20px;
  }
  
  .module-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.overview-section,
.modules-section,
.activity-section {
  animation: fadeInUp 0.6s ease-out;
}

.overview-section {
  animation-delay: 0.1s;
}

.modules-section {
  animation-delay: 0.2s;
}

.activity-section {
  animation-delay: 0.3s;
}
