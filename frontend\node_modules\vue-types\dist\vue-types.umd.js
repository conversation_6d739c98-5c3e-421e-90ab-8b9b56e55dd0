!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).VueTypes={})}(this,function(e){function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function n(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function i(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function o(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)t.indexOf(n=o[r])>=0||(i[n]=e[n]);return i}function u(e){return 1==(null!=(t=e)&&"object"==typeof t&&!1===Array.isArray(t))&&"[object Object]"===Object.prototype.toString.call(e);var t}var a=Object.prototype,f=a.toString,c=a.hasOwnProperty,l=/^\s*function (\w+)/;function s(e){var t,n=null!==(t=null==e?void 0:e.type)&&void 0!==t?t:e;if(n){var r=n.toString().match(l);return r?r[1]:""}return""}var y=function(e){var t,n;return!1!==u(e)&&"function"==typeof(t=e.constructor)&&!1!==u(n=t.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")},p=function(e){return e},v=function(e,t){return c.call(e,t)},d=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},h=Array.isArray||function(e){return"[object Array]"===f.call(e)},b=function(e){return"[object Function]"===f.call(e)},O=function(e){return y(e)&&v(e,"_vueTypes_name")},g=function(e){return y(e)&&(v(e,"type")||["_vueTypes_name","validator","default","required"].some(function(t){return v(e,t)}))};function m(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function j(e,t,n){var r;void 0===n&&(n=!1);var i=!0,o="";r=y(e)?e:{type:e};var u=O(r)?r._vueTypes_name+" - ":"";if(g(r)&&null!==r.type){if(void 0===r.type||!0===r.type)return i;if(!r.required&&void 0===t)return i;h(r.type)?(i=r.type.some(function(e){return!0===j(e,t,!0)}),o=r.type.map(function(e){return s(e)}).join(" or ")):i="Array"===(o=s(r))?h(t):"Object"===o?y(t):"String"===o||"Number"===o||"Boolean"===o||"Function"===o?function(e){if(null==e)return"";var t=e.constructor.toString().match(l);return t?t[1]:""}(t)===o:t instanceof r.type}if(!i){var a=u+'value "'+t+'" should be of type "'+o+'"';return!1===n?(p(a),!1):a}if(v(r,"validator")&&b(r.validator)){var f=p,c=[];if(p=function(e){c.push(e)},i=r.validator(t),p=f,!i){var d=(c.length>1?"* ":"")+c.join("\n* ");return c.length=0,!1===n?(p(d),i):d}}return i}function _(e,t){var n=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get:function(){return this.required=!0,this}},def:{value:function(e){return void 0!==e||this.default?b(e)||!0===j(this,e,!0)?(this.default=h(e)?function(){return[].concat(e)}:y(e)?function(){return Object.assign({},e)}:e,this):(p(this._vueTypes_name+' - invalid default value: "'+e+'"'),this):this}}}),r=n.validator;return b(r)&&(n.validator=m(r,n)),n}function T(e,t){var n=_(e,t);return Object.defineProperty(n,"validate",{value:function(e){return b(this.validator)&&p(this._vueTypes_name+" - calling .validate() will overwrite the current custom validator function. Validator info:\n"+JSON.stringify(this)),this.validator=m(e,this),this}})}function w(e,t,n){var r,i,u=(r=t,i={},Object.getOwnPropertyNames(r).forEach(function(e){i[e]=Object.getOwnPropertyDescriptor(r,e)}),Object.defineProperties({},i));if(u._vueTypes_name=e,!y(n))return u;var a,f,c=n.validator,l=o(n,["validator"]);if(b(c)){var s=u.validator;s&&(s=null!==(f=(a=s).__original)&&void 0!==f?f:a),u.validator=m(s?function(e){return s.call(this,e)&&c.call(this,e)}:c,u)}return Object.assign(u,l)}function k(e){return e.replace(/^(?!\s*$)/gm,"  ")}var P=function(){return T("any",{})},x=function(){return T("function",{type:Function})},A=function(){return T("boolean",{type:Boolean})},q=function(){return T("string",{type:String})},S=function(){return T("number",{type:Number})},V=function(){return T("array",{type:Array})},E=function(){return T("object",{type:Object})},N=function(){return _("integer",{type:Number,validator:function(e){return d(e)}})},F=function(){return _("symbol",{validator:function(e){return"symbol"==typeof e}})};function L(e,t){if(void 0===t&&(t="custom validation failed"),"function"!=typeof e)throw new TypeError("[VueTypes error]: You must provide a function as argument");return _(e.name||"<<anonymous function>>",{validator:function(n){var r=e(n);return r||p(this._vueTypes_name+" - "+t),r}})}function Y(e){if(!h(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");var t='oneOf - value should be one of "'+e.join('", "')+'".',n=e.reduce(function(e,t){if(null!=t){var n=t.constructor;-1===e.indexOf(n)&&e.push(n)}return e},[]);return _("oneOf",{type:n.length>0?n:void 0,validator:function(n){var r=-1!==e.indexOf(n);return r||p(t),r}})}function B(e){if(!h(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");for(var t=!1,n=[],r=0;r<e.length;r+=1){var i=e[r];if(g(i)){if(O(i)&&"oneOf"===i._vueTypes_name){n=n.concat(i.type);continue}if(b(i.validator)&&(t=!0),!0!==i.type&&i.type){n=n.concat(i.type);continue}}n.push(i)}return n=n.filter(function(e,t){return n.indexOf(e)===t}),_("oneOfType",t?{type:n,validator:function(t){var n=[],r=e.some(function(e){var r=j(O(e)&&"oneOf"===e._vueTypes_name?e.type||null:e,t,!0);return"string"==typeof r&&n.push(r),!0===r});return r||p("oneOfType - provided value does not match any of the "+n.length+" passed-in validators:\n"+k(n.join("\n"))),r}}:{type:n})}function D(e){return _("arrayOf",{type:Array,validator:function(t){var n,r=t.every(function(t){return!0===(n=j(e,t,!0))});return r||p("arrayOf - value validation error:\n"+k(n)),r}})}function I(e){return _("instanceOf",{type:e})}function J(e){return _("objectOf",{type:Object,validator:function(t){var n,r=Object.keys(t).every(function(r){return!0===(n=j(e,t[r],!0))});return r||p("objectOf - value validation error:\n"+k(n)),r}})}function M(e){var t=Object.keys(e),n=t.filter(function(t){var n;return!!(null===(n=e[t])||void 0===n?void 0:n.required)}),r=_("shape",{type:Object,validator:function(r){var i=this;if(!y(r))return!1;var o=Object.keys(r);if(n.length>0&&n.some(function(e){return-1===o.indexOf(e)})){var u=n.filter(function(e){return-1===o.indexOf(e)});return p(1===u.length?'shape - required property "'+u[0]+'" is not defined.':'shape - required properties "'+u.join('", "')+'" are not defined.'),!1}return o.every(function(n){if(-1===t.indexOf(n))return!0===i._vueTypes_isLoose||(p('shape - shape definition does not include a "'+n+'" property. Allowed keys: "'+t.join('", "')+'".'),!1);var o=j(e[n],r[n],!0);return"string"==typeof o&&p('shape - "'+n+'" property validation error:\n '+k(o)),!0===o})}});return Object.defineProperty(r,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(r,"loose",{get:function(){return this._vueTypes_isLoose=!0,this}}),r}var R=function(){function e(){}return e.extend=function(e){var t=this;if(h(e))return e.forEach(function(e){return t.extend(e)}),this;var n=e.name,r=e.validate,i=void 0!==r&&r,u=e.getter,a=void 0!==u&&u,f=o(e,["name","validate","getter"]);if(v(this,n))throw new TypeError('[VueTypes error]: Type "'+n+'" already defined');var c,l=f.type;return O(l)?(delete f.type,Object.defineProperty(this,n,a?{get:function(){return w(n,l,f)}}:{value:function(){var e,t=w(n,l,f);return t.validator&&(t.validator=(e=t.validator).bind.apply(e,[t].concat([].slice.call(arguments)))),t}})):(c=a?{get:function(){var e=Object.assign({},f);return i?T(n,e):_(n,e)},enumerable:!0}:{value:function(){var e,t,r=Object.assign({},f);return e=i?T(n,r):_(n,r),r.validator&&(e.validator=(t=r.validator).bind.apply(t,[e].concat([].slice.call(arguments)))),e},enumerable:!0},Object.defineProperty(this,n,c))},n(e,null,[{key:"any",get:function(){return P()}},{key:"func",get:function(){return x().def(this.defaults.func)}},{key:"bool",get:function(){return A().def(this.defaults.bool)}},{key:"string",get:function(){return q().def(this.defaults.string)}},{key:"number",get:function(){return S().def(this.defaults.number)}},{key:"array",get:function(){return V().def(this.defaults.array)}},{key:"object",get:function(){return E().def(this.defaults.object)}},{key:"integer",get:function(){return N().def(this.defaults.integer)}},{key:"symbol",get:function(){return F()}}]),e}();function $(e){var t;return void 0===e&&(e={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(t=function(t){function o(){return t.apply(this,arguments)||this}return i(o,t),n(o,null,[{key:"sensibleDefaults",get:function(){return r({},this.defaults)},set:function(t){this.defaults=!1!==t?r({},!0!==t?t:e):{}}}]),o}(R)).defaults=r({},e),t}R.defaults={},R.custom=L,R.oneOf=Y,R.instanceOf=I,R.oneOfType=B,R.arrayOf=D,R.objectOf=J,R.shape=M,R.utils={validate:function(e,t){return!0===j(t,e,!0)},toType:function(e,t,n){return void 0===n&&(n=!1),n?T(e,t):_(e,t)}};var z=function(e){function t(){return e.apply(this,arguments)||this}return i(t,e),t}($());e.any=P,e.array=V,e.arrayOf=D,e.bool=A,e.createTypes=$,e.custom=L,e.default=z,e.fromType=w,e.func=x,e.instanceOf=I,e.integer=N,e.number=S,e.object=E,e.objectOf=J,e.oneOf=Y,e.oneOfType=B,e.shape=M,e.string=q,e.symbol=F,e.toType=_,e.toValidableType=T,e.validateType=j});
//# sourceMappingURL=vue-types.umd.js.map
