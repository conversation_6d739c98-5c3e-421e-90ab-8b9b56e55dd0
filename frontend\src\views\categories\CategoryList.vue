<template>
  <div>
    <!-- 操作栏 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索分类名称"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        
        <a-col :span="16" style="text-align: right;">
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined />
            添加分类
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 分类表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="categories"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个分类吗？"
                @confirm="deleteCategory(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑分类模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑分类' : '添加分类'"
      @ok="handleSubmit"
      @cancel="resetForm"
    >
      <a-form :model="form" layout="vertical" ref="formRef">
        <a-form-item
          label="分类名称"
          name="name"
          :rules="[{ required: true, message: '请输入分类名称' }]"
        >
          <a-input v-model:value="form.name" placeholder="请输入分类名称" />
        </a-form-item>
        
        <a-form-item label="分类描述" name="description">
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入分类描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { categoryAPI } from '@/services/api'
import { PlusOutlined } from '@ant-design/icons-vue'

const categories = ref([])
const loading = ref(false)
const searchText = ref('')
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const form = ref({
  id: null,
  name: '',
  description: ''
})

const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

const fetchCategories = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.value.current,
      page_size: pagination.value.pageSize
    }
    
    if (searchText.value) {
      params.search = searchText.value
    }
    
    const response = await categoryAPI.getCategories(params)
    categories.value = response.results || []
    pagination.value.total = response.count || 0
  } catch (error) {
    message.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.value.current = 1
  fetchCategories()
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchCategories()
}

const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

const showEditModal = (record) => {
  isEdit.value = true
  form.value = { ...record }
  modalVisible.value = true
}

const resetForm = () => {
  form.value = {
    id: null,
    name: '',
    description: ''
  }
  modalVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await categoryAPI.updateCategory(form.value.id, form.value)
      message.success('更新成功')
    } else {
      await categoryAPI.createCategory(form.value)
      message.success('创建成功')
    }
    
    resetForm()
    fetchCategories()
  } catch (error) {
    if (error.errorFields) {
      return // 表单验证错误
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const deleteCategory = async (id) => {
  try {
    await categoryAPI.deleteCategory(id)
    message.success('删除成功')
    fetchCategories()
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  fetchCategories()
})
</script>
