<template>
  <a-layout style="min-height: 100vh">
    <!-- 顶部头部：Logo + 水平菜单 + 用户区 -->
    <a-layout-header class="top-header">
      <div class="topbar">
        <div class="brand" @click="goHome">
          <span class="brand-logo">AM</span>
          <span class="brand-name">资产管理系统</span>
        </div>

        <a-menu
          mode="horizontal"
          :selectedKeys="selectedKeys"
          @click="handleMenuClick"
        >
          <a-menu-item key="/dashboard">
            <DashboardOutlined />
            仪表板
          </a-menu-item>
          <a-menu-item key="/assets">
            <LaptopOutlined />
            资产
          </a-menu-item>
          <a-menu-item key="/categories">
            <AppstoreOutlined />
            分类
          </a-menu-item>
          <a-menu-item key="/locations">
            <EnvironmentOutlined />
            位置
          </a-menu-item>
          <a-menu-item key="/maintenance">
            <ToolOutlined />
            维修
          </a-menu-item>
          <a-menu-item key="/transfers">
            <SwapOutlined />
            转移
          </a-menu-item>
        </a-menu>

        <div class="user-area">
          <a-badge :count="5">
            <BellOutlined class="icon" />
          </a-badge>

          <a-dropdown>
            <a-avatar style="background-color: #87d068">
              <UserOutlined />
            </a-avatar>
            <template #overlay>
              <a-menu @click="onUserMenuClick">
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </a-layout-header>

    <!-- 面包屑 -->
    <div class="breadcrumb-wrap">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <HomeOutlined />
          首页
        </a-breadcrumb-item>
        <a-breadcrumb-item v-if="currentRoute.meta?.title">
          {{ currentRoute.meta.title }}
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 内容区 -->
    <a-layout-content class="content">
      <router-view />
    </a-layout-content>
  </a-layout>
  </template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  DashboardOutlined,
  LaptopOutlined,
  AppstoreOutlined,
  EnvironmentOutlined,
  ToolOutlined,
  SwapOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  HomeOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

const selectedKeys = ref([route.path])
const currentRoute = computed(() => route)

const handleMenuClick = ({ key }) => {
  selectedKeys.value = [key]
  if (route.path !== key) router.push(key)
}

// 监听路由变化更新选中的菜单项
router.afterEach((to) => {
  selectedKeys.value = [to.path]
})

const goHome = () => router.push('/dashboard')

const onUserMenuClick = ({ key }) => {
  if (key === 'logout') {
    localStorage.removeItem('token')
    localStorage.removeItem('refresh_token')
    router.replace({ path: '/login', query: { redirect: encodeURIComponent(route.fullPath) } })
  }
}
</script>

<style scoped>
.top-header {
  background: #fff;
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.topbar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.brand {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.brand-logo {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #1677ff;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
}

.brand-name {
  font-weight: 600;
}

.user-area {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon {
  font-size: 16px;
}

.breadcrumb-wrap {
  padding: 12px 24px 0;
}

.content {
  margin: 16px 24px 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}
</style>
