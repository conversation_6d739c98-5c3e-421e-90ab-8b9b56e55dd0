<template>
  <div class="modern-layout">
    <!-- 顶部导航栏 -->
    <header class="top-header">
      <div class="header-container">
        <!-- 左侧品牌区 -->
        <div class="brand-section" @click="goHome">
          <div class="brand-logo">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
          </div>
          <div class="brand-text">
            <h1 class="brand-title">智能资产管理</h1>
            <span class="brand-subtitle">Asset Management System</span>
          </div>
        </div>

        <!-- 中间导航菜单 -->
        <nav class="nav-menu">
          <div class="nav-items">
            <div
              v-for="item in menuItems"
              :key="item.key"
              :class="['nav-item', { active: selectedKeys.includes(item.key) }]"
              @click="handleMenuClick({ key: item.key })"
            >
              <component :is="item.icon" class="nav-icon" />
              <span class="nav-text">{{ item.label }}</span>
              <div class="nav-indicator"></div>
            </div>
          </div>
        </nav>

        <!-- 右侧用户区 -->
        <div class="user-section">
          <!-- 搜索框 -->
          <div class="search-box">
            <a-input
              placeholder="搜索..."
              class="search-input"
              :bordered="false"
            >
              <template #prefix>
                <SearchOutlined class="search-icon" />
              </template>
            </a-input>
          </div>

          <!-- 通知 -->
          <div class="notification-btn">
            <a-badge :count="5" :offset="[2, -2]">
              <BellOutlined class="action-icon" />
            </a-badge>
          </div>

          <!-- 全屏切换 -->
          <div class="fullscreen-btn" @click="toggleFullscreen">
            <FullscreenOutlined class="action-icon" />
          </div>

          <!-- 用户头像 -->
          <a-dropdown placement="bottomRight" :trigger="['click']">
            <div class="user-avatar">
              <a-avatar size="large" class="avatar">
                <UserOutlined />
              </a-avatar>
              <div class="user-info">
                <span class="user-name">管理员</span>
                <span class="user-role">系统管理员</span>
              </div>
            </div>
            <template #overlay>
              <a-menu class="user-menu" @click="onUserMenuClick">
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </header>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
      <div class="breadcrumb-container">
        <a-breadcrumb class="breadcrumb">
          <a-breadcrumb-item>
            <HomeOutlined />
            <span>首页</span>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-if="currentRoute.meta?.title">
            {{ currentRoute.meta.title }}
          </a-breadcrumb-item>
        </a-breadcrumb>

        <!-- 页面标题 -->
        <div class="page-title" v-if="currentRoute.meta?.title">
          <h2>{{ currentRoute.meta.title }}</h2>
          <p v-if="currentRoute.meta?.description">{{ currentRoute.meta.description }}</p>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <main class="main-content">
      <div class="content-container">
        <router-view />
      </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-left">
          <span>© 2024 智能资产管理系统. All rights reserved.</span>
        </div>
        <div class="footer-right">
          <span>Version 2.0.1</span>
          <span>•</span>
          <a href="#" class="footer-link">帮助文档</a>
          <span>•</span>
          <a href="#" class="footer-link">技术支持</a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  DashboardOutlined,
  LaptopOutlined,
  AppstoreOutlined,
  EnvironmentOutlined,
  ToolOutlined,
  SwapOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  HomeOutlined,
  SearchOutlined,
  FullscreenOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

const selectedKeys = ref([route.path])
const currentRoute = computed(() => route)

// 菜单项配置
const menuItems = ref([
  {
    key: '/dashboard',
    label: '仪表板',
    icon: DashboardOutlined
  },
  {
    key: '/assets',
    label: '资产管理',
    icon: LaptopOutlined
  },
  {
    key: '/categories',
    label: '分类管理',
    icon: AppstoreOutlined
  },
  {
    key: '/locations',
    label: '位置管理',
    icon: EnvironmentOutlined
  },
  {
    key: '/maintenance',
    label: '维修管理',
    icon: ToolOutlined
  },
  {
    key: '/transfers',
    label: '转移记录',
    icon: SwapOutlined
  }
])

const handleMenuClick = ({ key }) => {
  selectedKeys.value = [key]
  if (route.path !== key) router.push(key)
}

// 监听路由变化更新选中的菜单项
router.afterEach((to) => {
  selectedKeys.value = [to.path]
})

const goHome = () => router.push('/dashboard')

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const onUserMenuClick = ({ key }) => {
  if (key === 'logout') {
    localStorage.removeItem('token')
    localStorage.removeItem('refresh_token')
    router.replace({ path: '/login', query: { redirect: encodeURIComponent(route.fullPath) } })
  } else if (key === 'profile') {
    router.push('/profile')
  } else if (key === 'settings') {
    router.push('/settings')
  }
}
</script>

<style scoped>
/* 全局布局 */
.modern-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 50%, #91d5ff 100%);
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.top-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 32px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 品牌区域 */
.brand-section {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.brand-section:hover {
  transform: translateY(-1px);
}

.brand-logo {
  position: relative;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.4);
}

.logo-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
}

.nav-items {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.nav-item:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  transform: translateY(-1px);
}

.nav-item.active {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
}

.nav-icon {
  font-size: 16px;
}

.nav-text {
  font-size: 14px;
}

.nav-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: #1890ff;
  transition: width 0.3s ease;
}

.nav-item.active .nav-indicator {
  width: 80%;
}

/* 用户区域 */
.user-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  position: relative;
}

.search-input {
  width: 240px;
  height: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
  border-color: #1890ff;
}

.search-icon {
  color: #8c8c8c;
}

.notification-btn,
.fullscreen-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.notification-btn:hover,
.fullscreen-btn:hover {
  background: rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.action-icon {
  font-size: 16px;
  color: #666;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.user-avatar:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.avatar {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
  border: 2px solid white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

/* 面包屑区域 */
.breadcrumb-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.breadcrumb-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb :deep(.ant-breadcrumb-link) {
  color: #666;
  font-weight: 500;
}

.breadcrumb :deep(.ant-breadcrumb-separator) {
  color: #ccc;
}

.page-title h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-title p {
  font-size: 14px;
  color: #8c8c8c;
  margin: 4px 0 0 0;
}

/* 主内容区 */
.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.content-container {
  max-width: 1600px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 300px);
}

/* 底部 */
.footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: auto;
}

.footer-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #8c8c8c;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-link {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #096dd9;
}

/* 用户菜单样式 */
.user-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.user-menu :deep(.ant-menu-item) {
  border-radius: 8px;
  margin: 4px 8px;
  transition: all 0.3s ease;
}

.user-menu :deep(.ant-menu-item:hover) {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-container {
    padding: 0 16px;
  }

  .search-input {
    width: 180px;
  }

  .user-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .nav-items {
    display: none;
  }

  .search-box {
    display: none;
  }

  .brand-text {
    display: none;
  }

  .content-container {
    padding: 16px;
    margin: 0 16px;
  }

  .breadcrumb-container {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-container {
  animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.3);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.5);
}
</style>
