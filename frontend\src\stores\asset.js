import { defineStore } from 'pinia'
import { assetAPI } from '../services/api'

export const useAssetStore = defineStore('asset', {
  state: () => ({
    assets: [],
    currentAsset: null,
    loading: false,
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    },
    filters: {},
    summary: null
  }),

  getters: {
    assetCount: (state) => state.assets.length,
    activeAssets: (state) => state.assets.filter(asset => asset.status === 'active'),
    inactiveAssets: (state) => state.assets.filter(asset => asset.status === 'inactive')
  },

  actions: {
    async fetchAssets(params = {}) {
      this.loading = true
      try {
        const response = await assetAPI.getAssets({
          page: this.pagination.current,
          page_size: this.pagination.pageSize,
          ...this.filters,
          ...params
        })
        
        this.assets = response.results || []
        this.pagination.total = response.count || 0
        
        return response
      } catch (error) {
        console.error('获取资产列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchAsset(id) {
      this.loading = true
      try {
        const asset = await assetAPI.getAsset(id)
        this.currentAsset = asset
        return asset
      } catch (error) {
        console.error('获取资产详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async createAsset(data) {
      try {
        const asset = await assetAPI.createAsset(data)
        this.assets.unshift(asset)
        return asset
      } catch (error) {
        console.error('创建资产失败:', error)
        throw error
      }
    },

    async updateAsset(id, data) {
      try {
        const asset = await assetAPI.updateAsset(id, data)
        const index = this.assets.findIndex(item => item.id === id)
        if (index !== -1) {
          this.assets[index] = asset
        }
        if (this.currentAsset && this.currentAsset.id === id) {
          this.currentAsset = asset
        }
        return asset
      } catch (error) {
        console.error('更新资产失败:', error)
        throw error
      }
    },

    async deleteAsset(id) {
      try {
        await assetAPI.deleteAsset(id)
        this.assets = this.assets.filter(asset => asset.id !== id)
        if (this.currentAsset && this.currentAsset.id === id) {
          this.currentAsset = null
        }
      } catch (error) {
        console.error('删除资产失败:', error)
        throw error
      }
    },

    async fetchSummary() {
      try {
        const summary = await assetAPI.getAssetSummary()
        this.summary = summary
        return summary
      } catch (error) {
        console.error('获取资产统计失败:', error)
        throw error
      }
    },

    async transferAsset(id, data) {
      try {
        const result = await assetAPI.transferAsset(id, data)
        // 刷新资产列表
        await this.fetchAssets()
        return result
      } catch (error) {
        console.error('资产转移失败:', error)
        throw error
      }
    },

    setFilters(filters) {
      this.filters = { ...filters }
    },

    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    clearCurrentAsset() {
      this.currentAsset = null
    }
  }
})
