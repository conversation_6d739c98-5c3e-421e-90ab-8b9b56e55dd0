<template>
  <div>
    <!-- 搜索和操作栏 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索资产名称、技术员"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        
        <a-col :span="4">
          <a-select
            v-model:value="filters.maintenance_type"
            placeholder="维修类型"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="repair">维修</a-select-option>
            <a-select-option value="maintenance">保养</a-select-option>
            <a-select-option value="inspection">检查</a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="6">
          <a-range-picker
            v-model:value="dateRange"
            style="width: 100%"
            @change="handleDateChange"
            placeholder="选择日期范围"
          />
        </a-col>
        
        <a-col :span="8" style="text-align: right;">
          <a-space>
            <a-button @click="resetFilters">重置</a-button>
            <a-button type="primary" @click="showCreateModal">
              <PlusOutlined />
              添加维修记录
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 维修记录表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="maintenanceRecords"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'asset_info'">
            <div>
              <div style="font-weight: bold;">{{ record.asset_name }}</div>
              <div style="color: #666; font-size: 12px;">{{ record.asset_number }}</div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'maintenance_type'">
            <a-tag :color="getMaintenanceTypeColor(record.maintenance_type)">
              {{ record.maintenance_type_display }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'cost'">
            <span v-if="record.cost">
              ¥{{ Number(record.cost).toLocaleString() }}
            </span>
            <span v-else>-</span>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这条维修记录吗？"
                @confirm="deleteRecord(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑维修记录模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑维修记录' : '添加维修记录'"
      @ok="handleSubmit"
      @cancel="resetForm"
      width="700px"
    >
      <a-form :model="form" layout="vertical" ref="formRef">
        <a-form-item
          label="资产"
          name="asset"
          :rules="[{ required: true, message: '请选择资产' }]"
        >
          <a-select
            v-model:value="form.asset"
            placeholder="选择资产"
            show-search
            :filter-option="filterAssetOption"
          >
            <a-select-option
              v-for="asset in assets"
              :key="asset.id"
              :value="asset.id"
            >
              {{ asset.asset_number }} - {{ asset.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="维修类型"
              name="maintenance_type"
              :rules="[{ required: true, message: '请选择维修类型' }]"
            >
              <a-select v-model:value="form.maintenance_type" placeholder="选择维修类型">
                <a-select-option value="repair">维修</a-select-option>
                <a-select-option value="maintenance">保养</a-select-option>
                <a-select-option value="inspection">检查</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item
              label="维修日期"
              name="maintenance_date"
              :rules="[{ required: true, message: '请选择维修日期' }]"
            >
              <a-date-picker
                v-model:value="form.maintenance_date"
                style="width: 100%"
                placeholder="选择维修日期"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="费用" name="cost">
              <a-input-number
                v-model:value="form.cost"
                style="width: 100%"
                placeholder="请输入费用"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="技术员" name="technician">
              <a-input v-model:value="form.technician" placeholder="请输入技术员姓名" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item
          label="维修描述"
          name="description"
          :rules="[{ required: true, message: '请输入维修描述' }]"
        >
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入维修描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { maintenanceAPI, assetAPI } from '../../services/api'
import { PlusOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const maintenanceRecords = ref([])
const assets = ref([])
const loading = ref(false)
const searchText = ref('')
const dateRange = ref([])
const filters = ref({
  maintenance_type: undefined
})

const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const form = ref({
  id: null,
  asset: undefined,
  maintenance_type: undefined,
  maintenance_date: undefined,
  cost: undefined,
  technician: '',
  description: ''
})

const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

const columns = [
  {
    title: '资产信息',
    key: 'asset_info',
    width: 200
  },
  {
    title: '维修类型',
    key: 'maintenance_type',
    filters: [
      { text: '维修', value: 'repair' },
      { text: '保养', value: 'maintenance' },
      { text: '检查', value: 'inspection' }
    ]
  },
  {
    title: '维修日期',
    dataIndex: 'maintenance_date',
    key: 'maintenance_date',
    sorter: true
  },
  {
    title: '费用',
    key: 'cost',
    sorter: true
  },
  {
    title: '技术员',
    dataIndex: 'technician',
    key: 'technician'
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'created_by_name',
    key: 'created_by_name'
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

const getMaintenanceTypeColor = (type) => {
  const colors = {
    repair: 'red',
    maintenance: 'blue',
    inspection: 'green'
  }
  return colors[type] || 'default'
}

const filterAssetOption = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const fetchMaintenanceRecords = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.value.current,
      page_size: pagination.value.pageSize,
      ...filters.value
    }
    
    if (searchText.value) {
      params.search = searchText.value
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.maintenance_date_after = dateRange.value[0].format('YYYY-MM-DD')
      params.maintenance_date_before = dateRange.value[1].format('YYYY-MM-DD')
    }
    
    const response = await maintenanceAPI.getMaintenanceRecords(params)
    maintenanceRecords.value = response.results || []
    pagination.value.total = response.count || 0
  } catch (error) {
    message.error('获取维修记录失败')
  } finally {
    loading.value = false
  }
}

const fetchAssets = async () => {
  try {
    const response = await assetAPI.getAssets({ page_size: 1000 })
    assets.value = response.results || []
  } catch (error) {
    console.error('获取资产列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.value.current = 1
  fetchMaintenanceRecords()
}

const handleFilterChange = () => {
  pagination.value.current = 1
  fetchMaintenanceRecords()
}

const handleDateChange = () => {
  pagination.value.current = 1
  fetchMaintenanceRecords()
}

const resetFilters = () => {
  searchText.value = ''
  dateRange.value = []
  filters.value = {
    maintenance_type: undefined
  }
  pagination.value.current = 1
  fetchMaintenanceRecords()
}

const handleTableChange = (pag, tableFilters, sorter) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchMaintenanceRecords()
}

const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

const showEditModal = (record) => {
  isEdit.value = true
  form.value = {
    ...record,
    maintenance_date: dayjs(record.maintenance_date)
  }
  modalVisible.value = true
}

const resetForm = () => {
  form.value = {
    id: null,
    asset: undefined,
    maintenance_type: undefined,
    maintenance_date: dayjs(),
    cost: undefined,
    technician: '',
    description: ''
  }
  modalVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const data = {
      ...form.value,
      maintenance_date: form.value.maintenance_date.format('YYYY-MM-DD')
    }
    
    if (isEdit.value) {
      await maintenanceAPI.updateMaintenanceRecord(form.value.id, data)
      message.success('更新成功')
    } else {
      await maintenanceAPI.createMaintenanceRecord(data)
      message.success('创建成功')
    }
    
    resetForm()
    fetchMaintenanceRecords()
  } catch (error) {
    if (error.errorFields) {
      return // 表单验证错误
    }
    message.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const deleteRecord = async (id) => {
  try {
    await maintenanceAPI.deleteMaintenanceRecord(id)
    message.success('删除成功')
    fetchMaintenanceRecords()
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  fetchMaintenanceRecords()
  fetchAssets()
})
</script>
