<template>
  <div>
    <!-- 搜索和操作栏 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索资产编号、名称、品牌等"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        
        <a-col :span="4">
          <a-select
            v-model:value="filters.status"
            placeholder="状态筛选"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="active">使用中</a-select-option>
            <a-select-option value="inactive">闲置</a-select-option>
            <a-select-option value="maintenance">维修中</a-select-option>
            <a-select-option value="scrapped">报废</a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="4">
          <a-select
            v-model:value="filters.category"
            placeholder="分类筛选"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="4">
          <a-select
            v-model:value="filters.location"
            placeholder="位置筛选"
            style="width: 100%"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="location in locations"
              :key="location.id"
              :value="location.id"
            >
              {{ location.name }}
            </a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="6" style="text-align: right;">
          <a-space>
            <a-button @click="resetFilters">重置</a-button>
            <a-button type="primary" @click="$router.push('/assets/create')">
              <PlusOutlined />
              添加资产
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 资产表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="assets"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'image'">
            <a-image
              v-if="record.image"
              :src="record.image"
              :width="50"
              :height="50"
              style="object-fit: cover; border-radius: 4px;"
            />
            <div v-else style="width: 50px; height: 50px; background: #f5f5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
              <FileImageOutlined style="color: #bfbfbf;" />
            </div>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ record.status_display }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'purchase_price'">
            <span v-if="record.purchase_price">
              ¥{{ Number(record.purchase_price).toLocaleString() }}
            </span>
            <span v-else>-</span>
          </template>
          
          <template v-else-if="column.key === 'current_value'">
            <span v-if="record.current_value">
              ¥{{ Number(record.current_value).toLocaleString() }}
            </span>
            <span v-else>-</span>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewAsset(record.id)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="editAsset(record.id)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="showTransferModal(record)">
                转移
              </a-button>
              <a-popconfirm
                title="确定要删除这个资产吗？"
                @confirm="deleteAsset(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 资产转移模态框 -->
    <a-modal
      v-model:open="transferModalVisible"
      title="资产转移"
      @ok="handleTransfer"
      @cancel="transferModalVisible = false"
    >
      <a-form :model="transferForm" layout="vertical">
        <a-form-item label="目标位置">
          <a-select v-model:value="transferForm.to_location" placeholder="选择目标位置">
            <a-select-option
              v-for="location in locations"
              :key="location.id"
              :value="location.id"
            >
              {{ location.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="新责任人">
          <a-select v-model:value="transferForm.to_person" placeholder="选择新责任人">
            <a-select-option
              v-for="user in users"
              :key="user.id"
              :value="user.id"
            >
              {{ user.username }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="转移日期">
          <a-date-picker
            v-model:value="transferForm.transfer_date"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="转移原因">
          <a-textarea
            v-model:value="transferForm.reason"
            placeholder="请输入转移原因"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAssetStore } from '../../stores/asset'
import { categoryAPI, locationAPI } from '../../services/api'
import { PlusOutlined, FileImageOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()
const assetStore = useAssetStore()

const searchText = ref('')
const filters = ref({
  status: undefined,
  category: undefined,
  location: undefined
})

const categories = ref([])
const locations = ref([])
const users = ref([])

const transferModalVisible = ref(false)
const transferForm = ref({
  to_location: undefined,
  to_person: undefined,
  transfer_date: dayjs(),
  reason: ''
})
const currentTransferAsset = ref(null)

const assets = computed(() => assetStore.assets)
const loading = computed(() => assetStore.loading)

const paginationConfig = computed(() => ({
  current: assetStore.pagination.current,
  pageSize: assetStore.pagination.pageSize,
  total: assetStore.pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

const columns = [
  {
    title: '图片',
    key: 'image',
    width: 80
  },
  {
    title: '资产编号',
    dataIndex: 'asset_number',
    key: 'asset_number',
    sorter: true
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '分类',
    dataIndex: 'category_name',
    key: 'category_name'
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand'
  },
  {
    title: '状态',
    key: 'status',
    filters: [
      { text: '使用中', value: 'active' },
      { text: '闲置', value: 'inactive' },
      { text: '维修中', value: 'maintenance' },
      { text: '报废', value: 'scrapped' }
    ]
  },
  {
    title: '位置',
    dataIndex: 'location_name',
    key: 'location_name'
  },
  {
    title: '采购价格',
    key: 'purchase_price',
    sorter: true
  },
  {
    title: '当前价值',
    key: 'current_value',
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    inactive: 'orange',
    maintenance: 'red',
    scrapped: 'default'
  }
  return colors[status] || 'default'
}

const handleSearch = () => {
  assetStore.setFilters({ search: searchText.value })
  assetStore.setPagination({ current: 1 })
  fetchAssets()
}

const handleFilterChange = () => {
  assetStore.setFilters(filters.value)
  assetStore.setPagination({ current: 1 })
  fetchAssets()
}

const resetFilters = () => {
  searchText.value = ''
  filters.value = {
    status: undefined,
    category: undefined,
    location: undefined
  }
  assetStore.setFilters({})
  assetStore.setPagination({ current: 1 })
  fetchAssets()
}

const handleTableChange = (pagination, filters, sorter) => {
  assetStore.setPagination({
    current: pagination.current,
    pageSize: pagination.pageSize
  })
  
  const ordering = sorter.order ? 
    (sorter.order === 'ascend' ? sorter.field : `-${sorter.field}`) : 
    undefined
    
  assetStore.setFilters({ ordering })
  fetchAssets()
}

const fetchAssets = () => {
  assetStore.fetchAssets()
}

const viewAsset = (id) => {
  router.push(`/assets/${id}`)
}

const editAsset = (id) => {
  router.push(`/assets/${id}/edit`)
}

const deleteAsset = async (id) => {
  try {
    await assetStore.deleteAsset(id)
    message.success('删除成功')
  } catch (error) {
    message.error('删除失败')
  }
}

const showTransferModal = (asset) => {
  currentTransferAsset.value = asset
  transferForm.value = {
    to_location: undefined,
    to_person: undefined,
    transfer_date: dayjs(),
    reason: ''
  }
  transferModalVisible.value = true
}

const handleTransfer = async () => {
  try {
    const data = {
      ...transferForm.value,
      transfer_date: transferForm.value.transfer_date.format('YYYY-MM-DD')
    }
    
    await assetStore.transferAsset(currentTransferAsset.value.id, data)
    message.success('转移成功')
    transferModalVisible.value = false
  } catch (error) {
    message.error('转移失败')
  }
}

const fetchCategories = async () => {
  try {
    const response = await categoryAPI.getCategories()
    categories.value = response.results || []
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const fetchLocations = async () => {
  try {
    const response = await locationAPI.getLocations()
    locations.value = response.results || []
  } catch (error) {
    console.error('获取位置失败:', error)
  }
}

onMounted(() => {
  fetchAssets()
  fetchCategories()
  fetchLocations()
})
</script>
