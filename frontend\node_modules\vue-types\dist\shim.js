function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function e(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}function n(){return(n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function r(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function o(t){return 1==(null!=(e=t)&&"object"==typeof e&&!1===Array.isArray(e))&&"[object Object]"===Object.prototype.toString.call(t);var e}function u(t){var e,n;return!1!==o(t)&&"function"==typeof(e=t.constructor)&&!1!==o(n=e.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")}var i=Object.defineProperty,c=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function f(t,e,n){void 0===e&&(e={}),void 0===n&&(n=!1);var r={_vueTypes_name:{value:t,writable:!0},def:{value:function(t){var e=this;return void 0!==t||e.default?(e.default=c(t)?function(){return[].concat(t)}:u(t)?function(){return Object.assign({},t)}:t,e):e}},isRequired:{get:function(){return this.required=!0,this}}};return n&&(r.validate={value:function(){}}),Object.assign(Object.defineProperties({validator:function(){return!0}},r),e)}var a=function(){return f("any",{},!0)},s=function(){return f("func",{type:Function},!0)},p=function(){return f("bool",{type:Boolean},!0)},l=function(){return f("string",{type:String},!0)},y=function(){return f("number",{type:Number},!0)},d=function(){return f("array",{type:Array},!0)},b=function(){return f("object",{type:Object},!0)},v=function(){return f("symbol")},g=function(){return f("integer",{type:Number})},O=function(t){return f("oneOf")},h=function(t){return f("custom")},j=function(t){return f("instanceOf",{type:t})},m=function(t){return f("oneOfType")},x=function(t){return f("arrayOf",{type:Array})},k=function(t){return f("objectOf",{type:Object})},_=function(t){return i(f("shape",{type:Object}),"loose",{get:function(){return this}})};function T(t,e,n,r,o){var u;void 0===r&&(r=!1),void 0===o&&(o=!1);var c=((u={})[r?"get":"value"]=function(){return f(e,n,o).def(r?t.defaults[e]:void 0)},u);return i(t,e,c)}var w=function(){function t(){}return t.extend=function(t){var e=t.validate,n=t.getter,r=void 0!==n&&n,o=t.type,i=void 0===o?null:o;return T(this,t.name,{type:u(i)&&i.type?null:i},r,!!e)},e(t,null,[{key:"any",get:function(){return a()}},{key:"func",get:function(){return s().def(this.defaults.func)}},{key:"bool",get:function(){return p().def(this.defaults.bool)}},{key:"string",get:function(){return l().def(this.defaults.string)}},{key:"number",get:function(){return y().def(this.defaults.number)}},{key:"array",get:function(){return d().def(this.defaults.array)}},{key:"object",get:function(){return b().def(this.defaults.object)}},{key:"symbol",get:function(){return v()}},{key:"integer",get:function(){return g().def(this.defaults.integer)}}]),t}();function A(t){var o;return void 0===t&&(t={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(o=function(o){function u(){return o.apply(this,arguments)||this}return r(u,o),e(u,null,[{key:"sensibleDefaults",get:function(){return n({},this.defaults)},set:function(e){this.defaults=!1!==e?n({},!0!==e?e:t):{}}}]),u}(w)).defaults=n({},t),o}w.defaults={},w.oneOf=O,w.custom=h,w.instanceOf=j,w.oneOfType=m,w.arrayOf=x,w.objectOf=k,w.shape=_,w.utils={toType:f,validate:function(){return!![].slice.call(arguments)}},"production"!==process.env.NODE_ENV&&console.warn("You are using the production shimmed version of VueTypes in a development build. Refer to https://github.com/dwightjack/vue-types#production-build to learn how to configure VueTypes for usage in multiple environments.");var P=function(t){function e(){return t.apply(this,arguments)||this}return r(e,t),e}(A());Object.defineProperty(exports,"__esModule",{value:!0}),exports.any=a,exports.array=d,exports.arrayOf=x,exports.bool=p,exports.createTypes=A,exports.custom=h,exports.default=P,exports.func=s,exports.instanceOf=j,exports.integer=g,exports.number=y,exports.object=b,exports.objectOf=k,exports.oneOf=O,exports.oneOfType=m,exports.shape=_,exports.string=l,exports.symbol=v;
//# sourceMappingURL=shim.js.map
