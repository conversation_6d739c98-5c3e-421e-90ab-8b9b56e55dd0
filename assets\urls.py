from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'categories', views.CategoryViewSet)
router.register(r'locations', views.LocationViewSet)
router.register(r'assets', views.AssetViewSet)
router.register(r'maintenance-records', views.MaintenanceRecordViewSet)
router.register(r'asset-transfers', views.AssetTransferViewSet)

urlpatterns = [
    path('api/', include(router.urls)),
]
