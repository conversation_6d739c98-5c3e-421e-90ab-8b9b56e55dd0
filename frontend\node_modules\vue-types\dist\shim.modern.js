function t(t){return 1==(null!=(e=t)&&"object"==typeof e&&!1===Array.isArray(e))&&"[object Object]"===Object.prototype.toString.call(t);var e}function e(e){var r,n;return!1!==t(e)&&"function"==typeof(r=e.constructor)&&!1!==t(n=r.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")}const r=Object.defineProperty,n=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function s(t,r={},s=!1){const a={_vueTypes_name:{value:t,writable:!0},def:{value(t){const r=this;return void 0!==t||r.default?(r.default=n(t)?()=>[].concat(t):e(t)?()=>Object.assign({},t):t,r):r}},isRequired:{get(){return this.required=!0,this}}};return s&&(a.validate={value(){}}),Object.assign(Object.defineProperties({validator:()=>!0},a),r)}const a=()=>s("any",{},!0),o=()=>s("func",{type:Function},!0),i=()=>s("bool",{type:Boolean},!0),u=()=>s("string",{type:String},!0),c=()=>s("number",{type:Number},!0),l=()=>s("array",{type:Array},!0),f=()=>s("object",{type:Object},!0),y=()=>s("symbol"),d=()=>s("integer",{type:Number}),p=t=>s("oneOf"),b=t=>s("custom"),g=t=>s("instanceOf",{type:t}),h=t=>s("oneOfType"),O=t=>s("arrayOf",{type:Array}),j=t=>s("objectOf",{type:Object}),v=t=>r(s("shape",{type:Object}),"loose",{get(){return this}});function m(t,e,n,a=!1,o=!1){return r(t,e,{[a?"get":"value"]:()=>s(e,n,o).def(a?t.defaults[e]:void 0)})}class A{static get any(){return a()}static get func(){return o().def(this.defaults.func)}static get bool(){return i().def(this.defaults.bool)}static get string(){return u().def(this.defaults.string)}static get number(){return c().def(this.defaults.number)}static get array(){return l().def(this.defaults.array)}static get object(){return f().def(this.defaults.object)}static get symbol(){return y()}static get integer(){return d().def(this.defaults.integer)}static extend(t){const{name:r,validate:n,getter:s=!1,type:a=null}=t;return m(this,r,{type:e(a)&&a.type?null:a},s,!!n)}}function T(t={func:()=>{},bool:!0,string:"",number:0,array:()=>[],object:()=>({}),integer:0}){var e;return(e=class extends A{static get sensibleDefaults(){return{...this.defaults}}static set sensibleDefaults(e){this.defaults=!1!==e?!0!==e?{...e}:{...t}:{}}}).defaults={...t},e}A.defaults={},A.oneOf=p,A.custom=b,A.instanceOf=g,A.oneOfType=h,A.arrayOf=O,A.objectOf=j,A.shape=v,A.utils={toType:s,validate:(...t)=>!!t},"production"!==process.env.NODE_ENV&&console.warn("You are using the production shimmed version of VueTypes in a development build. Refer to https://github.com/dwightjack/vue-types#production-build to learn how to configure VueTypes for usage in multiple environments.");class w extends(T()){}export default w;export{a as any,l as array,O as arrayOf,i as bool,T as createTypes,b as custom,o as func,g as instanceOf,d as integer,c as number,f as object,j as objectOf,p as oneOf,h as oneOfType,v as shape,u as string,y as symbol};
//# sourceMappingURL=shim.modern.js.map
