from django.contrib import admin
from .models import Category, Location, Asset, MaintenanceRecord, AssetTransfer


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    search_fields = ['name']
    list_filter = ['created_at']


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'address', 'contact_person', 'contact_phone', 'created_at']
    search_fields = ['name', 'address', 'contact_person']
    list_filter = ['created_at']


@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ['asset_number', 'name', 'category', 'brand', 'status', 'location', 'responsible_person', 'created_at']
    list_filter = ['status', 'category', 'location', 'created_at']
    search_fields = ['asset_number', 'name', 'brand', 'model', 'serial_number']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('基本信息', {
            'fields': ('asset_number', 'name', 'category', 'description')
        }),
        ('产品信息', {
            'fields': ('brand', 'model', 'serial_number', 'image')
        }),
        ('财务信息', {
            'fields': ('purchase_date', 'purchase_price', 'current_value')
        }),
        ('管理信息', {
            'fields': ('status', 'location', 'responsible_person')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(MaintenanceRecord)
class MaintenanceRecordAdmin(admin.ModelAdmin):
    list_display = ['asset', 'maintenance_type', 'maintenance_date', 'cost', 'technician', 'created_by']
    list_filter = ['maintenance_type', 'maintenance_date', 'created_at']
    search_fields = ['asset__name', 'asset__asset_number', 'technician', 'description']
    date_hierarchy = 'maintenance_date'


@admin.register(AssetTransfer)
class AssetTransferAdmin(admin.ModelAdmin):
    list_display = ['asset', 'from_location', 'to_location', 'from_person', 'to_person', 'transfer_date', 'created_by']
    list_filter = ['transfer_date', 'from_location', 'to_location']
    search_fields = ['asset__name', 'asset__asset_number', 'reason']
    date_hierarchy = 'transfer_date'
