{"version": 3, "file": "index.js", "sources": ["../throttle.js", "../debounce.js"], "sourcesContent": ["/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param {number} delay -                  A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher)\n *                                            are most useful.\n * @param {Function} callback -               A function to be executed after delay milliseconds. The `this` context and all arguments are passed through,\n *                                            as-is, to `callback` when the throttled-function is executed.\n * @param {object} [options] -              An object to configure options.\n * @param {boolean} [options.noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds\n *                                            while the throttled-function is being called. If noTrailing is false or unspecified, callback will be executed\n *                                            one final time after the last throttled-function call. (After the throttled-function has not been called for\n *                                            `delay` milliseconds, the internal counter is reset).\n * @param {boolean} [options.noLeading] -   Optional, defaults to false. If noLeading is false, the first throttled-function call will execute callback\n *                                            immediately. If noLeading is true, the first the callback execution will be skipped. It should be noted that\n *                                            callback will never executed if both noLeading = true and noTrailing = true.\n * @param {boolean} [options.debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is\n *                                            false (at end), schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function} A new, throttled, function.\n */\nexport default function (delay, callback, options) {\n\tconst {\n\t\tnoTrailing = false,\n\t\tnoLeading = false,\n\t\tdebounceMode = undefined\n\t} = options || {};\n\t/*\n\t * After wrapper has stopped being called, this timeout ensures that\n\t * `callback` is executed at the proper times in `throttle` and `end`\n\t * debounce modes.\n\t */\n\tlet timeoutID;\n\tlet cancelled = false;\n\n\t// Keep track of the last time `callback` was executed.\n\tlet lastExec = 0;\n\n\t// Function to clear existing timeout\n\tfunction clearExistingTimeout() {\n\t\tif (timeoutID) {\n\t\t\tclearTimeout(timeoutID);\n\t\t}\n\t}\n\n\t// Function to cancel next exec\n\tfunction cancel(options) {\n\t\tconst { upcomingOnly = false } = options || {};\n\t\tclearExistingTimeout();\n\t\tcancelled = !upcomingOnly;\n\t}\n\n\t/*\n\t * The `wrapper` function encapsulates all of the throttling / debouncing\n\t * functionality and when executed will limit the rate at which `callback`\n\t * is executed.\n\t */\n\tfunction wrapper(...arguments_) {\n\t\tlet self = this;\n\t\tlet elapsed = Date.now() - lastExec;\n\n\t\tif (cancelled) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Execute `callback` and update the `lastExec` timestamp.\n\t\tfunction exec() {\n\t\t\tlastExec = Date.now();\n\t\t\tcallback.apply(self, arguments_);\n\t\t}\n\n\t\t/*\n\t\t * If `debounceMode` is true (at begin) this is used to clear the flag\n\t\t * to allow future `callback` executions.\n\t\t */\n\t\tfunction clear() {\n\t\t\ttimeoutID = undefined;\n\t\t}\n\n\t\tif (!noLeading && debounceMode && !timeoutID) {\n\t\t\t/*\n\t\t\t * Since `wrapper` is being called for the first time and\n\t\t\t * `debounceMode` is true (at begin), execute `callback`\n\t\t\t * and noLeading != true.\n\t\t\t */\n\t\t\texec();\n\t\t}\n\n\t\tclearExistingTimeout();\n\n\t\tif (debounceMode === undefined && elapsed > delay) {\n\t\t\tif (noLeading) {\n\t\t\t\t/*\n\t\t\t\t * In throttle mode with noLeading, if `delay` time has\n\t\t\t\t * been exceeded, update `lastExec` and schedule `callback`\n\t\t\t\t * to execute after `delay` ms.\n\t\t\t\t */\n\t\t\t\tlastExec = Date.now();\n\t\t\t\tif (!noTrailing) {\n\t\t\t\t\ttimeoutID = setTimeout(debounceMode ? clear : exec, delay);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t/*\n\t\t\t\t * In throttle mode without noLeading, if `delay` time has been exceeded, execute\n\t\t\t\t * `callback`.\n\t\t\t\t */\n\t\t\t\texec();\n\t\t\t}\n\t\t} else if (noTrailing !== true) {\n\t\t\t/*\n\t\t\t * In trailing throttle mode, since `delay` time has not been\n\t\t\t * exceeded, schedule `callback` to execute `delay` ms after most\n\t\t\t * recent execution.\n\t\t\t *\n\t\t\t * If `debounceMode` is true (at begin), schedule `clear` to execute\n\t\t\t * after `delay` ms.\n\t\t\t *\n\t\t\t * If `debounceMode` is false (at end), schedule `callback` to\n\t\t\t * execute after `delay` ms.\n\t\t\t */\n\t\t\ttimeoutID = setTimeout(\n\t\t\t\tdebounceMode ? clear : exec,\n\t\t\t\tdebounceMode === undefined ? delay - elapsed : delay\n\t\t\t);\n\t\t}\n\t}\n\n\twrapper.cancel = cancel;\n\n\t// Return the wrapper function.\n\treturn wrapper;\n}\n", "/* eslint-disable no-undefined */\n\nimport throttle from './throttle.js';\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param {number} delay -               A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param {Function} callback -          A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                        to `callback` when the debounced-function is executed.\n * @param {object} [options] -           An object to configure options.\n * @param {boolean} [options.atBegin] -  Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                        after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                        (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n *\n * @returns {Function} A new, debounced function.\n */\nexport default function (delay, callback, options) {\n\tconst { atBegin = false } = options || {};\n\treturn throttle(delay, callback, { debounceMode: atBegin !== false });\n}\n"], "names": ["delay", "callback", "options", "_ref", "_ref$noTrailing", "noTrailing", "_ref$noLeading", "noLeading", "_ref$debounceMode", "debounceMode", "undefined", "timeoutID", "cancelled", "lastExec", "clearExistingTimeout", "clearTimeout", "cancel", "_ref2", "_ref2$upcomingOnly", "upcomingOnly", "wrapper", "_len", "arguments", "length", "arguments_", "Array", "_key", "self", "elapsed", "Date", "now", "exec", "apply", "clear", "setTimeout", "_ref$atBegin", "atBegin", "throttle"], "mappings": ";;;;;;CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,mBAAUA,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;CAClD,EAAA,IAAAC,IAAA,GAIID,OAAO,IAAI,EAAE;KAAAE,eAAA,GAAAD,IAAA,CAHhBE,UAAU;CAAVA,IAAAA,UAAU,GAAAD,eAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,eAAA;KAAAE,cAAA,GAAAH,IAAA,CAClBI,SAAS;CAATA,IAAAA,SAAS,GAAAD,cAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,cAAA;KAAAE,iBAAA,GAAAL,IAAA,CACjBM,YAAY;CAAZA,IAAAA,YAAY,GAAAD,iBAAA,KAAGE,KAAAA,CAAAA,GAAAA,SAAS,GAAAF,iBAAA,CAAA;CAEzB;CACD;CACA;CACA;CACA;CACC,EAAA,IAAIG,SAAS,CAAA;GACb,IAAIC,SAAS,GAAG,KAAK,CAAA;;CAErB;GACA,IAAIC,QAAQ,GAAG,CAAC,CAAA;;CAEhB;GACA,SAASC,oBAAoBA,GAAG;CAC/B,IAAA,IAAIH,SAAS,EAAE;OACdI,YAAY,CAACJ,SAAS,CAAC,CAAA;CACxB,KAAA;CACD,GAAA;;CAEA;GACA,SAASK,MAAMA,CAACd,OAAO,EAAE;CACxB,IAAA,IAAAe,KAAA,GAAiCf,OAAO,IAAI,EAAE;OAAAgB,kBAAA,GAAAD,KAAA,CAAtCE,YAAY;CAAZA,MAAAA,YAAY,GAAAD,kBAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,kBAAA,CAAA;CAC5BJ,IAAAA,oBAAoB,EAAE,CAAA;KACtBF,SAAS,GAAG,CAACO,YAAY,CAAA;CAC1B,GAAA;;CAEA;CACD;CACA;CACA;CACA;GACC,SAASC,OAAOA,GAAgB;CAAA,IAAA,KAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,GAAAC,IAAAA,KAAA,CAAAJ,IAAA,GAAAK,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA,EAAA,EAAA;CAAVF,MAAAA,UAAU,CAAAE,IAAA,CAAAJ,GAAAA,SAAA,CAAAI,IAAA,CAAA,CAAA;CAAA,KAAA;KAC7B,IAAIC,IAAI,GAAG,IAAI,CAAA;KACf,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAGjB,QAAQ,CAAA;CAEnC,IAAA,IAAID,SAAS,EAAE;CACd,MAAA,OAAA;CACD,KAAA;;CAEA;KACA,SAASmB,IAAIA,GAAG;CACflB,MAAAA,QAAQ,GAAGgB,IAAI,CAACC,GAAG,EAAE,CAAA;CACrB7B,MAAAA,QAAQ,CAAC+B,KAAK,CAACL,IAAI,EAAEH,UAAU,CAAC,CAAA;CACjC,KAAA;;CAEA;CACF;CACA;CACA;KACE,SAASS,KAAKA,GAAG;CAChBtB,MAAAA,SAAS,GAAGD,SAAS,CAAA;CACtB,KAAA;CAEA,IAAA,IAAI,CAACH,SAAS,IAAIE,YAAY,IAAI,CAACE,SAAS,EAAE;CAC7C;CACH;CACA;CACA;CACA;CACGoB,MAAAA,IAAI,EAAE,CAAA;CACP,KAAA;CAEAjB,IAAAA,oBAAoB,EAAE,CAAA;CAEtB,IAAA,IAAIL,YAAY,KAAKC,SAAS,IAAIkB,OAAO,GAAG5B,KAAK,EAAE;CAClD,MAAA,IAAIO,SAAS,EAAE;CACd;CACJ;CACA;CACA;CACA;CACIM,QAAAA,QAAQ,GAAGgB,IAAI,CAACC,GAAG,EAAE,CAAA;SACrB,IAAI,CAACzB,UAAU,EAAE;WAChBM,SAAS,GAAGuB,UAAU,CAACzB,YAAY,GAAGwB,KAAK,GAAGF,IAAI,EAAE/B,KAAK,CAAC,CAAA;CAC3D,SAAA;CACD,OAAC,MAAM;CACN;CACJ;CACA;CACA;CACI+B,QAAAA,IAAI,EAAE,CAAA;CACP,OAAA;CACD,KAAC,MAAM,IAAI1B,UAAU,KAAK,IAAI,EAAE;CAC/B;CACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACGM,MAAAA,SAAS,GAAGuB,UAAU,CACrBzB,YAAY,GAAGwB,KAAK,GAAGF,IAAI,EAC3BtB,YAAY,KAAKC,SAAS,GAAGV,KAAK,GAAG4B,OAAO,GAAG5B,KAChD,CAAC,CAAA;CACF,KAAA;CACD,GAAA;GAEAoB,OAAO,CAACJ,MAAM,GAAGA,MAAM,CAAA;;CAEvB;CACA,EAAA,OAAOI,OAAO,CAAA;CACf;;CCrIA;;CAIA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,mBAAUpB,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;CAClD,EAAA,IAAAC,IAAA,GAA4BD,OAAO,IAAI,EAAE;KAAAiC,YAAA,GAAAhC,IAAA,CAAjCiC,OAAO;CAAPA,IAAAA,OAAO,GAAAD,YAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,YAAA,CAAA;CACvB,EAAA,OAAOE,QAAQ,CAACrC,KAAK,EAAEC,QAAQ,EAAE;KAAEQ,YAAY,EAAE2B,OAAO,KAAK,KAAA;CAAM,GAAC,CAAC,CAAA;CACtE;;;;;;;;;;;"}