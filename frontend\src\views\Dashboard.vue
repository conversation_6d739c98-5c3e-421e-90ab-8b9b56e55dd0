<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">智能资产仪表盘</h1>
          <p class="page-subtitle">实时监控和分析您的资产状况</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" class="refresh-btn" @click="fetchData">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="(card, index) in statsCards" :key="index">
          <div class="stat-card" :class="`stat-card-${index + 1}`">
            <div class="stat-icon">
              <component :is="card.icon" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ card.value }}</div>
              <div class="stat-title">{{ card.title }}</div>
              <div class="stat-trend" :class="card.trend">
                <component :is="card.trendIcon" />
                <span>{{ card.trendText }}</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :lg="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>资产分类统计</h3>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="export">导出数据</a-menu-item>
                    <a-menu-item key="detail">查看详情</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div class="chart-content">
              <div ref="categoryChart" class="chart"></div>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :lg="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>位置分布统计</h3>
              <a-dropdown>
                <a-button type="text" size="small">
                  <MoreOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="export">导出数据</a-menu-item>
                    <a-menu-item key="detail">查看详情</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div class="chart-content">
              <div ref="locationChart" class="chart"></div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 状态分布区域 -->
    <div class="status-section">
      <div class="status-card">
        <div class="status-header">
          <h3>资产状态分布</h3>
          <div class="status-summary">
            <span>总计 {{ summary?.total_assets || 0 }} 项资产</span>
          </div>
        </div>
        <div class="status-content">
          <a-row :gutter="[32, 32]">
            <a-col :xs="12" :sm="6" v-for="(status, index) in statusItems" :key="index">
              <div class="status-item">
                <div class="status-progress">
                  <a-progress
                    type="circle"
                    :percent="status.percent"
                    :stroke-color="status.color"
                    :size="120"
                    :stroke-width="8"
                  >
                    <template #format="percent">
                      <div class="progress-content">
                        <div class="progress-value">{{ status.value }}</div>
                        <div class="progress-percent">{{ percent }}%</div>
                      </div>
                    </template>
                  </a-progress>
                </div>
                <div class="status-label">{{ status.label }}</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <div class="quick-actions-card">
        <div class="actions-header">
          <h3>快速操作</h3>
        </div>
        <div class="actions-content">
          <a-row :gutter="[16, 16]">
            <a-col :xs="12" :sm="8" :md="6" v-for="action in quickActions" :key="action.key">
              <div class="action-item" @click="handleQuickAction(action.key)">
                <div class="action-icon">
                  <component :is="action.icon" />
                </div>
                <div class="action-text">{{ action.label }}</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAssetStore } from '../stores/asset'
import { assetAPI } from '../services/api'
import {
  LaptopOutlined,
  CheckCircleOutlined,
  ToolOutlined,
  DollarOutlined,
  ReloadOutlined,
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const assetStore = useAssetStore()
const summary = ref(null)
const categoryStats = ref([])
const locationStats = ref([])

// 统计卡片数据
const statsCards = computed(() => [
  {
    title: '总资产数量',
    value: summary.value?.total_assets || 0,
    icon: LaptopOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+12% 较上月'
  },
  {
    title: '使用中资产',
    value: summary.value?.active_assets || 0,
    icon: CheckCircleOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+8% 较上月'
  },
  {
    title: '维修中资产',
    value: summary.value?.maintenance_assets || 0,
    icon: ToolOutlined,
    trend: 'down',
    trendIcon: ArrowDownOutlined,
    trendText: '-3% 较上月'
  },
  {
    title: '资产总价值',
    value: `¥${(summary.value?.total_value || 0).toLocaleString()}`,
    icon: DollarOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+15% 较上月'
  }
])

// 状态分布数据
const statusItems = computed(() => [
  {
    label: '使用中',
    value: summary.value?.active_assets || 0,
    percent: activePercent.value,
    color: '#1890ff'
  },
  {
    label: '闲置',
    value: summary.value?.inactive_assets || 0,
    percent: inactivePercent.value,
    color: '#52c41a'
  },
  {
    label: '维修中',
    value: summary.value?.maintenance_assets || 0,
    percent: maintenancePercent.value,
    color: '#faad14'
  },
  {
    label: '报废',
    value: summary.value?.scrapped_assets || 0,
    percent: scrappedPercent.value,
    color: '#ff4d4f'
  }
])

// 快速操作数据
const quickActions = ref([
  {
    key: 'add-asset',
    label: '添加资产',
    icon: PlusOutlined
  },
  {
    key: 'search-asset',
    label: '查找资产',
    icon: SearchOutlined
  },
  {
    key: 'maintenance-report',
    label: '维修报告',
    icon: FileTextOutlined
  },
  {
    key: 'system-settings',
    label: '系统设置',
    icon: SettingOutlined
  }
])

const activePercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.active_assets / summary.value.total_assets) * 100)
})

const inactivePercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.inactive_assets / summary.value.total_assets) * 100)
})

const maintenancePercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.maintenance_assets / summary.value.total_assets) * 100)
})

const scrappedPercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.scrapped_assets / summary.value.total_assets) * 100)
})

const fetchData = async () => {
  try {
    // 获取统计数据
    summary.value = await assetAPI.getAssetSummary()

    // 获取分类统计
    categoryStats.value = await assetAPI.getAssetsByCategory()

    // 获取位置统计
    locationStats.value = await assetAPI.getAssetsByLocation()

  } catch (error) {
    console.error('获取仪表板数据失败:', error)
  }
}

const handleQuickAction = (key) => {
  switch (key) {
    case 'add-asset':
      router.push('/assets/create')
      break
    case 'search-asset':
      router.push('/assets')
      break
    case 'maintenance-report':
      router.push('/maintenance')
      break
    case 'system-settings':
      router.push('/settings')
      break
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* 整体容器 */
.dashboard-container {
  padding: 0;
  background: transparent;
}

/* 页面头部 */
.dashboard-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  gap: 16px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #096dd9);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.stat-card-1::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.stat-card-2::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.stat-card-3::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.stat-card-4::before {
  background: linear-gradient(90deg, #f5222d, #ff4d4f);
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.stat-card-1 .stat-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-card-2 .stat-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-card-3 .stat-icon {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.stat-card-4 .stat-icon {
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up {
  color: #52c41a;
}

.stat-trend.down {
  color: #ff4d4f;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.chart-content {
  padding: 24px;
}

.chart {
  height: 300px;
  width: 100%;
}

/* 状态分布区域 */
.status-section {
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.status-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.status-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.status-summary {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.status-content {
  padding: 32px 24px;
}

.status-item {
  text-align: center;
}

.status-progress {
  margin-bottom: 16px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-value {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.progress-percent {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.status-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 快速操作区域 */
.quick-actions-section {
  margin-bottom: 24px;
}

.quick-actions-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.quick-actions-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.actions-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.actions-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.actions-content {
  padding: 24px;
}

.action-item {
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
  border: 1px solid rgba(24, 144, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-item:hover {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
}

.action-icon {
  font-size: 24px;
  color: #1890ff;
  transition: color 0.3s ease;
}

.action-item:hover .action-icon {
  color: white;
}

.action-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;
}

.action-item:hover .action-text {
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stat-card {
    height: auto;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 24px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }

  .chart-content,
  .status-content,
  .actions-content {
    padding: 16px;
  }

  .action-item {
    height: 80px;
    padding: 16px;
  }

  .action-icon {
    font-size: 20px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-section,
.charts-section,
.status-section,
.quick-actions-section {
  animation: fadeInUp 0.6s ease-out;
}

.stats-section {
  animation-delay: 0.1s;
}

.charts-section {
  animation-delay: 0.2s;
}

.status-section {
  animation-delay: 0.3s;
}

.quick-actions-section {
  animation-delay: 0.4s;
}

/* Ant Design 组件样式覆盖 */
:deep(.ant-progress-circle .ant-progress-text) {
  color: #1890ff !important;
  font-weight: 600;
}

:deep(.ant-btn-text) {
  color: #666;
}

:deep(.ant-btn-text:hover) {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}
</style>
