<template>
  <div class="dashboard-container">
    <div class="page-title mb-6">
      <h1>资产仪表盘</h1>
      <p class="text-gray-500">实时监控和分析您的资产状况</p>
    </div>

    <a-row :gutter="16" class="mb-8 animate-fade-in">
      <a-col :xs="24" :sm="12" :md="6" v-for="(item, index) in statsCards" :key=
        <a-card>
          <a-statistic
            title="总资产数量"
            :value="summary?.total_assets || 0"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <LaptopOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="使用中资产"
            :value="summary?.active_assets || 0"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="维修中资产"
            :value="summary?.maintenance_assets || 0"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <ToolOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="资产总价值"
            :value="summary?.total_value || 0"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#cf1322' }"
          >
            <template #prefix>
              <DollarOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="按分类统计" class="mb-24">
          <div ref="categoryChart" class="chart"></div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="按位置统计" class="mb-24">
          <div ref="locationChart" class="chart"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16">
      <a-col :span="24">
        <a-card title="资产状态分布">
          <a-row :gutter="16">
            <a-col :span="6">
              <div class="center p-20">
                <a-progress
                  type="circle"
                  :percent="activePercent"
                  :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }"
                />
                <div style="margin-top: 8px;">使用中</div>
              </div>
            </a-col>
            
            <a-col :span="6">
              <div class="center p-20">
                <a-progress
                  type="circle"
                  :percent="inactivePercent"
                  stroke-color="#faad14"
                />
                <div style="margin-top: 8px;">闲置</div>
              </div>
            </a-col>
            
            <a-col :span="6">
              <div class="center p-20">
                <a-progress
                  type="circle"
                  :percent="maintenancePercent"
                  stroke-color="#ff7875"
                />
                <div style="margin-top: 8px;">维修中</div>
              </div>
            </a-col>
            
            <a-col :span="6">
              <div class="center p-20">
                <a-progress
                  type="circle"
                  :percent="scrappedPercent"
                  stroke-color="#bfbfbf"
                />
                <div style="margin-top: 8px;">报废</div>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAssetStore } from '../stores/asset'
import { assetAPI } from '../services/api'
import {
  LaptopOutlined,
  CheckCircleOutlined,
  ToolOutlined,
  DollarOutlined
} from '@ant-design/icons-vue'

const assetStore = useAssetStore()
const summary = ref(null)
const categoryStats = ref([])
const locationStats = ref([])

const activePercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.active_assets / summary.value.total_assets) * 100)
})

const inactivePercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.inactive_assets / summary.value.total_assets) * 100)
})

const maintenancePercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.maintenance_assets / summary.value.total_assets) * 100)
})

const scrappedPercent = computed(() => {
  if (!summary.value || summary.value.total_assets === 0) return 0
  return Math.round((summary.value.scrapped_assets / summary.value.total_assets) * 100)
})

const fetchData = async () => {
  try {
    // 获取统计数据
    summary.value = await assetAPI.getAssetSummary()
    
    // 获取分类统计
    categoryStats.value = await assetAPI.getAssetsByCategory()
    
    // 获取位置统计
    locationStats.value = await assetAPI.getAssetsByLocation()
    
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.ant-statistic-content {
  font-size: 24px;
}
</style>
