<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="(card, index) in statsCards" :key="index">
          <div class="stat-card" :class="`stat-card-${index + 1}`">
            <div class="stat-icon">
              <component :is="card.icon" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ card.value }}</div>
              <div class="stat-title">{{ card.title }}</div>
              <div class="stat-trend" :class="card.trend">
                <component :is="card.trendIcon" />
                <span>{{ card.trendText }}</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- TOP5监控区域 -->
    <div class="top5-section">
      <a-row :gutter="[24, 24]">
        <!-- CPU使用率TOP5 -->
        <a-col :xs="24" :lg="12">
          <div class="top5-card">
            <div class="top5-header">
              <h3>
                CPU使用率 TOP5
              </h3>
              <a-button type="text" size="small" @click="refreshCpuData">
                <ReloadOutlined />
              </a-button>
            </div>
            <div class="top5-content">
              <div class="top5-item" v-for="(item, index) in cpuTop5" :key="item.id">
                <div class="item-rank">{{ index + 1 }}</div>
                <div class="item-info">
                  <div class="item-name">{{ item.hostname }}</div>
                  <div class="item-detail">{{ item.ip }}</div>
                </div>
                <div class="item-value">
                  <div class="value-text">{{ item.usage }}%</div>
                  <a-progress
                    :percent="item.usage"
                    :stroke-color="getCpuColor(item.usage)"
                    :show-info="false"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 内存使用率TOP5 -->
        <a-col :xs="24" :lg="12">
          <div class="top5-card">
            <div class="top5-header">
              <h3>
                <DatabaseOutlined />
                内存使用率 TOP5
              </h3>
              <a-button type="text" size="small" @click="refreshMemoryData">
                <ReloadOutlined />
              </a-button>
            </div>
            <div class="top5-content">
              <div class="top5-item" v-for="(item, index) in memoryTop5" :key="item.id">
                <div class="item-rank">{{ index + 1 }}</div>
                <div class="item-info">
                  <div class="item-name">{{ item.hostname }}</div>
                  <div class="item-detail">{{ item.total }} / {{ item.used }}</div>
                </div>
                <div class="item-value">
                  <div class="value-text">{{ item.usage }}%</div>
                  <a-progress
                    :percent="item.usage"
                    :stroke-color="getMemoryColor(item.usage)"
                    :show-info="false"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="[24, 24]" style="margin-top: 24px;">
        <!-- 文件使用率TOP5 -->
        <a-col :xs="24" :lg="12">
          <div class="top5-card">
            <div class="top5-header">
              <h3>
                <FolderOutlined />
                文件使用率 TOP5
              </h3>
              <a-button type="text" size="small" @click="refreshDiskData">
                <ReloadOutlined />
              </a-button>
            </div>
            <div class="top5-content">
              <div class="top5-item" v-for="(item, index) in diskTop5" :key="item.id">
                <div class="item-rank">{{ index + 1 }}</div>
                <div class="item-info">
                  <div class="item-name">{{ item.hostname }}</div>
                  <div class="item-detail">{{ item.mount }} ({{ item.filesystem }})</div>
                </div>
                <div class="item-value">
                  <div class="value-text">{{ item.usage }}%</div>
                  <a-progress
                    :percent="item.usage"
                    :stroke-color="getDiskColor(item.usage)"
                    :show-info="false"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 网络速率TOP5 -->
        <a-col :xs="24" :lg="12">
          <div class="top5-card">
            <div class="top5-header">
              <h3>
                <WifiOutlined />
                网络速率 TOP5
              </h3>
              <a-button type="text" size="small" @click="refreshNetworkData">
                <ReloadOutlined />
              </a-button>
            </div>
            <div class="top5-content">
              <div class="top5-item" v-for="(item, index) in networkTop5" :key="item.id">
                <div class="item-rank">{{ index + 1 }}</div>
                <div class="item-info">
                  <div class="item-name">{{ item.hostname }}</div>
                  <div class="item-detail">{{ item.interface }}</div>
                </div>
                <div class="item-value">
                  <div class="value-text">{{ item.speed }}</div>
                  <div class="network-detail">
                    <span class="in-speed">↓ {{ item.inSpeed }}</span>
                    <span class="out-speed">↑ {{ item.outSpeed }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 告警区域 -->
    <div class="alerts-section">
      <a-row :gutter="[24, 24]">
        <!-- 最新告警 -->
        <a-col :xs="24" :lg="16">
          <div class="alerts-card">
            <div class="alerts-header">
              <h3>
                <AlertOutlined />
                最新告警
              </h3>
              <div class="header-actions">
                <a-button type="text" size="small" @click="refreshAlerts">
                  <ReloadOutlined />
                </a-button>
                <a-button type="text" size="small" @click="viewAllAlerts">
                  查看全部
                </a-button>
              </div>
            </div>
            <div class="alerts-content">
              <div class="alert-item" v-for="alert in latestAlerts" :key="alert.id">
                <div class="alert-severity" :class="alert.severity">
                  <component :is="getSeverityIcon(alert.severity)" />
                </div>
                <div class="alert-info">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-description">{{ alert.description }}</div>
                  <div class="alert-meta">
                    <span class="alert-host">{{ alert.hostname }}</span>
                    <span class="alert-time">{{ alert.time }}</span>
                  </div>
                </div>
                <div class="alert-actions">
                  <a-button type="text" size="small" @click="acknowledgeAlert(alert)">
                    确认
                  </a-button>
                  <a-button type="text" size="small" @click="viewAlertDetail(alert)">
                    详情
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 告警统计 -->
        <a-col :xs="24" :lg="8">
          <div class="alert-stats-card">
            <div class="stats-header">
              <h3>
                <BarChartOutlined />
                告警统计
              </h3>
            </div>
            <div class="stats-content">
              <div class="stat-item">
                <div class="stat-label">今日告警数</div>
                <div class="stat-value today-alerts">{{ todayAlertsCount }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">未处理告警</div>
                <div class="stat-value pending-alerts">{{ pendingAlertsCount }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">高危告警</div>
                <div class="stat-value critical-alerts">{{ criticalAlertsCount }}</div>
              </div>

              <!-- 告警趋势图 -->
              <div class="alert-trend">
                <h4>24小时告警趋势</h4>
                <div ref="alertTrendChart" class="trend-chart"></div>
              </div>

              <!-- 告警分布 -->
              <div class="alert-distribution">
                <h4>告警级别分布</h4>
                <div class="distribution-item" v-for="item in alertDistribution" :key="item.level">
                  <div class="distribution-label">
                    <span class="level-dot" :style="{ backgroundColor: item.color }"></span>
                    {{ item.label }}
                  </div>
                  <div class="distribution-value">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  DatabaseOutlined,
  FolderOutlined,
  WifiOutlined,
  AlertOutlined,
  BarChartOutlined,
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

// 统计卡片数据
const statsCards = computed(() => [
  {
    title: '在线主机',
    value: onlineHosts.value,
    icon: CheckCircleOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+2 较昨日'
  },
  {
    title: '今日告警',
    value: todayAlertsCount.value,
    icon: AlertOutlined,
    trend: 'down',
    trendIcon: ArrowDownOutlined,
    trendText: '-15 较昨日'
  },
  {
    title: '未处理告警',
    value: pendingAlertsCount.value,
    icon: WarningOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+3 较昨日'
  },
  {
    title: '监控项目',
    value: monitoringItems.value,
    icon: BarChartOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+25 较上周'
  }
])

// 基础统计数据
const onlineHosts = ref(156)
const todayAlertsCount = ref(23)
const pendingAlertsCount = ref(8)
const monitoringItems = ref(1247)

// CPU使用率TOP5数据
const cpuTop5 = ref([
  {
    id: 1,
    hostname: 'web-server-01',
    ip: '************',
    usage: 89.5
  },
  {
    id: 2,
    hostname: 'db-server-01',
    ip: '************',
    usage: 76.3
  },
  {
    id: 3,
    hostname: 'app-server-02',
    ip: '************',
    usage: 68.7
  },
  {
    id: 4,
    hostname: 'cache-server-01',
    ip: '************',
    usage: 54.2
  },
  {
    id: 5,
    hostname: 'file-server-01',
    ip: '************',
    usage: 45.8
  }
])

// 内存使用率TOP5数据
const memoryTop5 = ref([
  {
    id: 1,
    hostname: 'db-server-01',
    total: '32GB',
    used: '28.5GB',
    usage: 89.1
  },
  {
    id: 2,
    hostname: 'web-server-01',
    total: '16GB',
    used: '12.8GB',
    usage: 80.0
  },
  {
    id: 3,
    hostname: 'app-server-02',
    total: '24GB',
    used: '17.2GB',
    usage: 71.7
  },
  {
    id: 4,
    hostname: 'cache-server-01',
    total: '8GB',
    used: '5.1GB',
    usage: 63.8
  },
  {
    id: 5,
    hostname: 'monitor-server',
    total: '16GB',
    used: '9.6GB',
    usage: 60.0
  }
])

// 文件使用率TOP5数据
const diskTop5 = ref([
  {
    id: 1,
    hostname: 'file-server-01',
    mount: '/data',
    filesystem: '/dev/sda1',
    usage: 92.3
  },
  {
    id: 2,
    hostname: 'db-server-01',
    mount: '/var/lib/mysql',
    filesystem: '/dev/sdb1',
    usage: 85.7
  },
  {
    id: 3,
    hostname: 'web-server-01',
    mount: '/var/log',
    filesystem: '/dev/sdc1',
    usage: 78.4
  },
  {
    id: 4,
    hostname: 'app-server-02',
    mount: '/opt/app',
    filesystem: '/dev/sdd1',
    usage: 65.2
  },
  {
    id: 5,
    hostname: 'backup-server',
    mount: '/backup',
    filesystem: '/dev/sde1',
    usage: 58.9
  }
])

// 网络速率TOP5数据
const networkTop5 = ref([
  {
    id: 1,
    hostname: 'web-server-01',
    interface: 'eth0',
    speed: '1.2 Gbps',
    inSpeed: '800 Mbps',
    outSpeed: '400 Mbps'
  },
  {
    id: 2,
    hostname: 'db-server-01',
    interface: 'eth0',
    speed: '950 Mbps',
    inSpeed: '600 Mbps',
    outSpeed: '350 Mbps'
  },
  {
    id: 3,
    hostname: 'file-server-01',
    interface: 'eth0',
    speed: '780 Mbps',
    inSpeed: '500 Mbps',
    outSpeed: '280 Mbps'
  },
  {
    id: 4,
    hostname: 'app-server-02',
    interface: 'eth0',
    speed: '650 Mbps',
    inSpeed: '420 Mbps',
    outSpeed: '230 Mbps'
  },
  {
    id: 5,
    hostname: 'cache-server-01',
    interface: 'eth0',
    speed: '520 Mbps',
    inSpeed: '340 Mbps',
    outSpeed: '180 Mbps'
  }
])

// 最新告警数据
const latestAlerts = ref([
  {
    id: 1,
    title: 'CPU使用率过高',
    description: 'web-server-01 CPU使用率达到89.5%，超过阈值80%',
    hostname: 'web-server-01',
    severity: 'critical',
    time: '2分钟前'
  },
  {
    id: 2,
    title: '磁盘空间不足',
    description: 'file-server-01 /data分区使用率92.3%，即将满载',
    hostname: 'file-server-01',
    severity: 'warning',
    time: '5分钟前'
  },
  {
    id: 3,
    title: '内存使用率告警',
    description: 'db-server-01 内存使用率89.1%，建议优化',
    hostname: 'db-server-01',
    severity: 'warning',
    time: '8分钟前'
  },
  {
    id: 4,
    title: '网络连接异常',
    description: 'app-server-03 网络连接中断，无法访问',
    hostname: 'app-server-03',
    severity: 'critical',
    time: '12分钟前'
  },
  {
    id: 5,
    title: '服务响应缓慢',
    description: 'web-server-02 HTTP响应时间超过5秒',
    hostname: 'web-server-02',
    severity: 'info',
    time: '15分钟前'
  }
])

// 告警级别分布
const alertDistribution = ref([
  {
    level: 'critical',
    label: '严重',
    count: 3,
    color: '#ff4d4f'
  },
  {
    level: 'warning',
    label: '警告',
    count: 12,
    color: '#faad14'
  },
  {
    level: 'info',
    label: '信息',
    count: 8,
    color: '#1890ff'
  }
])

// 颜色获取函数
const getCpuColor = (usage) => {
  if (usage >= 80) return '#ff4d4f'
  if (usage >= 60) return '#faad14'
  return '#52c41a'
}

const getMemoryColor = (usage) => {
  if (usage >= 85) return '#ff4d4f'
  if (usage >= 70) return '#faad14'
  return '#52c41a'
}

const getDiskColor = (usage) => {
  if (usage >= 90) return '#ff4d4f'
  if (usage >= 75) return '#faad14'
  return '#52c41a'
}

const getSeverityIcon = (severity) => {
  const icons = {
    critical: ExclamationCircleOutlined,
    warning: WarningOutlined,
    info: InfoCircleOutlined
  }
  return icons[severity] || InfoCircleOutlined
}

// 事件处理函数
const refreshCpuData = () => {
  console.log('刷新CPU数据')
  // 这里可以调用API刷新数据
}

const refreshMemoryData = () => {
  console.log('刷新内存数据')
}

const refreshDiskData = () => {
  console.log('刷新磁盘数据')
}

const refreshNetworkData = () => {
  console.log('刷新网络数据')
}

const refreshAlerts = () => {
  console.log('刷新告警数据')
}

const viewAllAlerts = () => {
  router.push('/alerts')
}

const acknowledgeAlert = (alert) => {
  console.log('确认告警:', alert)
}

const viewAlertDetail = (alert) => {
  console.log('查看告警详情:', alert)
}


onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* 整体容器 */
.dashboard-container {
  padding: 0;
  background: transparent;
}

/* 页面头部 */

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  gap: 16px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  border-radius: 8px;
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #096dd9);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.stat-card-1::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.stat-card-2::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.stat-card-3::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.stat-card-4::before {
  background: linear-gradient(90deg, #f5222d, #ff4d4f);
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.stat-card-1 .stat-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-card-2 .stat-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-card-3 .stat-icon {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.stat-card-4 .stat-icon {
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up {
  color: #52c41a;
}

.stat-trend.down {
  color: #ff4d4f;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.chart-content {
  padding: 24px;
}

.chart {
  height: 300px;
  width: 100%;
}

/* 状态分布区域 */
.status-section {
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.status-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.status-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.status-summary {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.status-content {
  padding: 32px 24px;
}

.status-item {
  text-align: center;
}

.status-progress {
  margin-bottom: 16px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-value {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.progress-percent {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.status-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 快速操作区域 */
.quick-actions-section {
  margin-bottom: 24px;
}

.quick-actions-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.quick-actions-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.actions-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.actions-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.actions-content {
  padding: 24px;
}

.action-item {
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
  border: 1px solid rgba(24, 144, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-item:hover {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
}

.action-icon {
  font-size: 24px;
  color: #1890ff;
  transition: color 0.3s ease;
}

.action-item:hover .action-icon {
  color: white;
}

.action-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;
}

.action-item:hover .action-text {
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stat-card {
    height: auto;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }

  .chart-content,
  .status-content,
  .actions-content {
    padding: 16px;
  }

  .action-item {
    height: 80px;
    padding: 16px;
  }

  .action-icon {
    font-size: 20px;
  }
}

/* TOP5监控区域样式 */
.top5-section {
  margin-bottom: 24px;
}

.top5-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.top5-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.top5-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.top5-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.top5-content {
  padding: 16px 24px;
}

.top5-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.top5-item:last-child {
  border-bottom: none;
}

.top5-item:hover {
  background: rgba(24, 144, 255, 0.02);
  border-radius: 8px;
  margin: 0 -12px;
  padding: 12px;
}

.item-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.item-detail {
  font-size: 12px;
  color: #8c8c8c;
}

.item-value {
  text-align: right;
  min-width: 80px;
}

.value-text {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.network-detail {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 11px;
}

.in-speed {
  color: #52c41a;
}

.out-speed {
  color: #faad14;
}

/* 告警区域样式 */
.alerts-section {
  margin-bottom: 24px;
}

.alerts-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.alerts-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.alerts-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.alerts-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.alerts-content {
  padding: 16px 24px;
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item:hover {
  background: rgba(24, 144, 255, 0.02);
  border-radius: 8px;
  margin: 0 -12px;
  padding: 16px 12px;
}

.alert-severity {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.alert-severity.critical {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.alert-severity.warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.alert-severity.info {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.alert-info {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.alert-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #8c8c8c;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 0;
}

/* 告警统计卡片 */
.alert-stats-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.alert-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.stats-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.stats-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-content {
  padding: 24px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
}

.today-alerts {
  color: #1890ff;
}

.pending-alerts {
  color: #faad14;
}

.critical-alerts {
  color: #ff4d4f;
}

.alert-trend {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.alert-trend h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.trend-chart {
  height: 120px;
  background: #fafafa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  font-size: 12px;
}

.alert-distribution {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.alert-distribution h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.distribution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.distribution-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.level-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.distribution-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-section,
.top5-section,
.alerts-section {
  animation: fadeInUp 0.6s ease-out;
}

.stats-section {
  animation-delay: 0.1s;
}

.top5-section {
  animation-delay: 0.2s;
}

.alerts-section {
  animation-delay: 0.3s;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top5-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-value {
    text-align: left;
    width: 100%;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .alert-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }

  .alert-meta {
    flex-direction: column;
    gap: 4px;
  }
}

/* Ant Design 组件样式覆盖 */
:deep(.ant-progress-line .ant-progress-bg) {
  border-radius: 4px;
}

:deep(.ant-btn-text) {
  color: #666;
}

:deep(.ant-btn-text:hover) {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}
</style>
