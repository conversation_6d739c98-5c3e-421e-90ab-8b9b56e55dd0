<template>
  <div>
    <a-card :title="isEdit ? '编辑资产' : '添加资产'">
      <a-form
        :model="form"
        :rules="rules"
        layout="vertical"
        @finish="handleSubmit"
        ref="formRef"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="资产编号" name="asset_number">
              <a-input v-model:value="form.asset_number" placeholder="请输入资产编号" />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="资产名称" name="name">
              <a-input v-model:value="form.name" placeholder="请输入资产名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="分类" name="category">
              <a-select v-model:value="form.category" placeholder="选择分类">
                <a-select-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="品牌" name="brand">
              <a-input v-model:value="form.brand" placeholder="请输入品牌" />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="型号" name="model">
              <a-input v-model:value="form.model" placeholder="请输入型号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="序列号" name="serial_number">
              <a-input v-model:value="form.serial_number" placeholder="请输入序列号" />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="form.status" placeholder="选择状态">
                <a-select-option value="active">使用中</a-select-option>
                <a-select-option value="inactive">闲置</a-select-option>
                <a-select-option value="maintenance">维修中</a-select-option>
                <a-select-option value="scrapped">报废</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="位置" name="location">
              <a-select v-model:value="form.location" placeholder="选择位置" allow-clear>
                <a-select-option
                  v-for="location in locations"
                  :key="location.id"
                  :value="location.id"
                >
                  {{ location.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="采购日期" name="purchase_date">
              <a-date-picker
                v-model:value="form.purchase_date"
                style="width: 100%"
                placeholder="选择采购日期"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="采购价格" name="purchase_price">
              <a-input-number
                v-model:value="form.purchase_price"
                style="width: 100%"
                placeholder="请输入采购价格"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="当前价值" name="current_value">
              <a-input-number
                v-model:value="form.current_value"
                style="width: 100%"
                placeholder="请输入当前价值"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="责任人" name="responsible_person">
          <a-select v-model:value="form.responsible_person" placeholder="选择责任人" allow-clear>
            <a-select-option
              v-for="user in users"
              :key="user.id"
              :value="user.id"
            >
              {{ user.username }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="资产图片" name="image">
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            list-type="picture-card"
            :max-count="1"
            @preview="handlePreview"
            @remove="handleRemove"
          >
            <div v-if="fileList.length < 1">
              <PlusOutlined />
              <div style="margin-top: 8px">上传图片</div>
            </div>
          </a-upload>
        </a-form-item>

        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入资产描述"
            :rows="4"
          />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              {{ isEdit ? '更新' : '创建' }}
            </a-button>
            <a-button @click="$router.go(-1)">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 图片预览模态框 -->
    <a-modal v-model:open="previewVisible" :footer="null">
      <img alt="预览" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAssetStore } from '../../stores/asset'
import { categoryAPI, locationAPI } from '../../services/api'
import { PlusOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const assetStore = useAssetStore()

const isEdit = computed(() => !!route.params.id)
const loading = ref(false)

const formRef = ref()
const form = ref({
  asset_number: '',
  name: '',
  category: undefined,
  brand: '',
  model: '',
  serial_number: '',
  status: 'active',
  location: undefined,
  purchase_date: undefined,
  purchase_price: undefined,
  current_value: undefined,
  responsible_person: undefined,
  description: '',
  image: null
})

const rules = {
  asset_number: [
    { required: true, message: '请输入资产编号' }
  ],
  name: [
    { required: true, message: '请输入资产名称' }
  ],
  category: [
    { required: true, message: '请选择分类' }
  ],
  status: [
    { required: true, message: '请选择状态' }
  ]
}

const categories = ref([])
const locations = ref([])
const users = ref([])

const fileList = ref([])
const previewVisible = ref(false)
const previewImage = ref('')

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  
  // 将文件转换为base64或处理上传
  form.value.image = file
  return false // 阻止自动上传
}

const handlePreview = async (file) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj)
  }
  previewImage.value = file.url || file.preview
  previewVisible.value = true
}

const handleRemove = () => {
  form.value.image = null
}

const getBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

const handleSubmit = async () => {
  try {
    loading.value = true
    
    const formData = new FormData()
    Object.keys(form.value).forEach(key => {
      if (form.value[key] !== undefined && form.value[key] !== null) {
        if (key === 'purchase_date' && form.value[key]) {
          formData.append(key, form.value[key].format('YYYY-MM-DD'))
        } else {
          formData.append(key, form.value[key])
        }
      }
    })

    if (isEdit.value) {
      await assetStore.updateAsset(route.params.id, formData)
      message.success('更新成功')
    } else {
      await assetStore.createAsset(formData)
      message.success('创建成功')
    }
    
    router.push('/assets')
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

const fetchCategories = async () => {
  try {
    const response = await categoryAPI.getCategories()
    categories.value = response.results || []
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const fetchLocations = async () => {
  try {
    const response = await locationAPI.getLocations()
    locations.value = response.results || []
  } catch (error) {
    console.error('获取位置失败:', error)
  }
}

const loadAssetData = async () => {
  if (isEdit.value) {
    try {
      const asset = await assetStore.fetchAsset(route.params.id)
      form.value = {
        ...asset,
        purchase_date: asset.purchase_date ? dayjs(asset.purchase_date) : undefined
      }
      
      if (asset.image) {
        fileList.value = [{
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: asset.image
        }]
      }
    } catch (error) {
      message.error('加载资产数据失败')
      router.push('/assets')
    }
  }
}

onMounted(() => {
  fetchCategories()
  fetchLocations()
  loadAssetData()
})
</script>
