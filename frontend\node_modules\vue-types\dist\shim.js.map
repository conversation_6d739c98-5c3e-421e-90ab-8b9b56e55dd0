{"version": 3, "file": "shim.js", "sources": ["../node_modules/is-plain-object/index.es.js", "../src/shim.ts", "../src/sensibles.ts", "../src/shim.cjs.ts"], "sourcesContent": ["/*!\n * isobject <https://github.com/jonschlinkert/isobject>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObject(val) {\n  return val != null && typeof val === 'object' && Array.isArray(val) === false;\n}\n\n/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObjectObject(o) {\n  return isObject(o) === true\n    && Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObjectObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (typeof ctor !== 'function') return false;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObjectObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n}\n\nexport default isPlainObject;\n", "import isPlainObject from 'is-plain-object'\nimport { typeDefaults } from './sensibles'\nimport { VueTypesDefaults } from './types'\nexport { VueTypeDef, VueTypeValidableDef } from './types'\nconst dfn = Object.defineProperty\n\nconst isArray =\n  Array.isArray ||\n  function (value) {\n    return Object.prototype.toString.call(value) === '[object Array]'\n  }\n\nfunction type<T = any>(name: string, props: any = {}, validable = false): T {\n  const descriptors: PropertyDescriptorMap = {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    def: {\n      value(v) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const t = this\n        if (v === undefined && !t.default) {\n          return t\n        }\n        if (isArray(v)) {\n          t.default = () => [].concat(v)\n        } else if (isPlainObject(v)) {\n          t.default = () => Object.assign({}, v)\n        } else {\n          t.default = v\n        }\n        return t\n      },\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n  }\n\n  if (validable) {\n    ;(descriptors as any).validate = {\n      // eslint-disable-next-line @typescript-eslint/no-empty-function\n      value() {},\n    }\n  }\n  return Object.assign(\n    Object.defineProperties(\n      {\n        validator: () => true,\n      },\n      descriptors,\n    ),\n    props,\n  )\n}\n\nexport const any = () => type('any', {}, true)\nexport const func = <T = any>() => type<T>('func', { type: Function }, true)\nexport const bool = () => type('bool', { type: Boolean }, true)\nexport const string = () => type('string', { type: String }, true)\nexport const number = () => type('number', { type: Number }, true)\nexport const array = <T = any>() => type<T>('array', { type: Array }, true)\nexport const object = <T = any>() => type<T>('object', { type: Object }, true)\nexport const symbol = () => type('symbol')\nexport const integer = () => type('integer', { type: Number })\n/* eslint-disable @typescript-eslint/no-unused-vars */\nexport const oneOf = <T = any>(a: any) => type<T>('oneOf')\nexport const custom = <T = any>(a: any) => type<T>('custom')\nexport const instanceOf = <T = any>(Constr: any) =>\n  type<T>('instanceOf', { type: Constr })\nexport const oneOfType = <T = any>(a: any) => type<T>('oneOfType')\nexport const arrayOf = <T = any>(a: any) => type<T>('arrayOf', { type: Array })\n\nexport const objectOf = <T = any>(a: any) =>\n  type<T>('objectOf', { type: Object })\nexport const shape = <T = any>(a: any) =>\n  dfn(\n    type<T>('shape', { type: Object }),\n    'loose',\n    {\n      get() {\n        return this\n      },\n    },\n  )\n/* eslint-enable @typescript-eslint/no-unused-vars */\n\nfunction createValidator(\n  root: any,\n  name: string,\n  props: any,\n  getter = false,\n  validable = false,\n) {\n  const prop = getter ? 'get' : 'value'\n  const descr = {\n    [prop]: () =>\n      type(name, props, validable).def(\n        getter ? root.defaults[name] : undefined,\n      ),\n  }\n\n  return dfn(root, name, descr)\n}\n\nclass BaseVueTypes {\n  static defaults: Partial<VueTypesDefaults> = {}\n\n  static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n  static get any() {\n    return any()\n  }\n  static get func() {\n    return func().def(this.defaults.func)\n  }\n  static get bool() {\n    return bool().def(this.defaults.bool)\n  }\n  static get string() {\n    return string().def(this.defaults.string)\n  }\n  static get number() {\n    return number().def(this.defaults.number)\n  }\n  static get array() {\n    return array().def(this.defaults.array)\n  }\n  static get object() {\n    return object().def(this.defaults.object)\n  }\n  static get symbol() {\n    return symbol()\n  }\n  static get integer() {\n    return integer().def(this.defaults.integer)\n  }\n  static oneOf = oneOf\n  static custom = custom\n  static instanceOf = instanceOf\n  static oneOfType = oneOfType\n  static arrayOf = arrayOf\n  static objectOf = objectOf\n  static shape = shape\n  static extend<T = any>(props): T {\n    const { name, validate, getter = false, type = null } = props\n    // If we are inheriting from a custom type, let's ignore the type property\n    const extType = isPlainObject(type) && type.type ? null : type\n    return createValidator(this, name, { type: extType }, getter, !!validate)\n  }\n  static utils = {\n    toType: type as (...args: any[]) => any,\n    validate: (...args: any[]) => !!args,\n  }\n}\n\nexport function createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\n/* eslint-disable no-console */\nif (process.env.NODE_ENV !== 'production') {\n  console.warn(\n    'You are using the production shimmed version of VueTypes in a development build. Refer to https://github.com/dwightjack/vue-types#production-build to learn how to configure VueTypes for usage in multiple environments.',\n  )\n}\n/* eslint-enable no-console */\n\nexport default class VueTypes extends createTypes() {}\n", "import { VueTypesDefaults } from './types'\n\nexport const typeDefaults = (): VueTypesDefaults => ({\n  func: () => undefined,\n  bool: true,\n  string: '',\n  number: 0,\n  array: () => [],\n  object: () => ({}),\n  integer: 0,\n})\n", "import VueTypes from './shim'\nObject.defineProperty(exports, '__esModule', {\n  value: true,\n})\n\nexport default VueTypes\n\nexport * from './shim'\n"], "names": ["isObjectObject", "o", "val", "Array", "isArray", "Object", "prototype", "toString", "call", "isPlainObject", "ctor", "prot", "constructor", "hasOwnProperty", "dfn", "defineProperty", "value", "type", "name", "props", "validable", "descriptors", "_vueTypes_name", "writable", "def", "v", "t", "this", "undefined", "default", "concat", "assign", "isRequired", "get", "required", "validate", "defineProperties", "validator", "any", "func", "Function", "bool", "Boolean", "string", "String", "number", "Number", "array", "object", "symbol", "integer", "oneOf", "a", "custom", "instanceOf", "Constr", "oneOfType", "arrayOf", "objectOf", "shape", "createValidator", "root", "getter", "descr", "defaults", "BaseVueTypes", "extend", "createTypes", "defs", "toType", "process", "env", "NODE_ENV", "console", "warn", "VueTypes", "exports"], "mappings": "6gBAkBA,SAASA,EAAeC,GACtB,OAAuB,IAXT,OADEC,EAYAD,IAXqB,iBAARC,IAA2C,IAAvBC,MAAMC,QAAQF,KAYpB,oBAAtCG,OAAOC,UAAUC,SAASC,KAAKP,GAbtC,IAAkBC,EAgBlB,SAASO,EAAcR,GACrB,IAAIS,EAAKC,EAET,OAA0B,IAAtBX,EAAeC,IAIC,mBADpBS,EAAOT,EAAEW,eAKoB,IAAzBZ,EADJW,EAAOD,EAAKJ,aAIiC,IAAzCK,EAAKE,eAAe,qBCjCpBC,EAAMT,OAAOU,eAEbX,EACJD,MAAMC,SACN,SAAUY,GACR,MAAiD,mBAA1CX,OAAOC,UAAUC,SAASC,KAAKQ,IAG1C,SAASC,EAAcC,EAAcC,EAAiBC,YAAjBD,IAAAA,EAAa,aAAIC,IAAAA,GAAY,GAChE,IAAMC,EAAqC,CACzCC,eAAgB,CACdN,MAAOE,EACPK,UAAU,GAEZC,IAAK,CACHR,eAAMS,GAEJ,IAAMC,EAAIC,KACV,YAAUC,IAANH,GAAoBC,EAAEG,SAIxBH,EAAEG,QADAzB,EAAQqB,GACE,iBAAM,GAAGK,OAAOL,IACnBhB,EAAcgB,GACX,kBAAMpB,OAAO0B,OAAO,GAAIN,IAExBA,EAEPC,GATEA,IAYbM,WAAY,CACVC,eAEE,OADAN,KAAKO,UAAW,UAYtB,OANId,IACAC,EAAoBc,SAAW,CAE/BnB,qBAGGX,OAAO0B,OACZ1B,OAAO+B,iBACL,CACEC,UAAW,sBAEbhB,GAEFF,GAISmB,IAAAA,EAAM,kBAAMrB,EAAK,MAAO,IAAI,IAC5BsB,EAAO,kBAAetB,EAAQ,OAAQ,CAAEA,KAAMuB,WAAY,IAC1DC,EAAO,kBAAMxB,EAAK,OAAQ,CAAEA,KAAMyB,UAAW,IAC7CC,EAAS,kBAAM1B,EAAK,SAAU,CAAEA,KAAM2B,SAAU,IAChDC,EAAS,kBAAM5B,EAAK,SAAU,CAAEA,KAAM6B,SAAU,IAChDC,EAAQ,kBAAe9B,EAAQ,QAAS,CAAEA,KAAMd,QAAS,IACzD6C,EAAS,kBAAe/B,EAAQ,SAAU,CAAEA,KAAMZ,SAAU,IAC5D4C,EAAS,kBAAMhC,EAAK,WACpBiC,EAAU,kBAAMjC,EAAK,UAAW,CAAEA,KAAM6B,UAExCK,EAAQ,SAAUC,UAAWnC,EAAQ,UACrCoC,EAAS,SAAUD,UAAWnC,EAAQ,WACtCqC,EAAa,SAAUC,UAClCtC,EAAQ,aAAc,CAAEA,KAAMsC,KACnBC,EAAY,SAAUJ,UAAWnC,EAAQ,cACzCwC,EAAU,SAAUL,UAAWnC,EAAQ,UAAW,CAAEA,KAAMd,SAE1DuD,EAAW,SAAUN,UAChCnC,EAAQ,WAAY,CAAEA,KAAMZ,UACjBsD,EAAQ,SAAUP,UAC7BtC,EACEG,EAAQ,QAAS,CAAEA,KAAMZ,SACzB,QACA,CACE4B,eACE,gBAMR,SAAS2B,EACPC,EACA3C,EACAC,EACA2C,EACA1C,kBADA0C,IAAAA,GAAS,YACT1C,IAAAA,GAAY,GAEZ,IACM2C,UADOD,EAAS,MAAQ,SAEpB,kBACN7C,EAAKC,EAAMC,EAAOC,GAAWI,IAC3BsC,EAASD,EAAKG,SAAS9C,QAAQU,OAIrC,OAAOd,EAAI+C,EAAM3C,EAAM6C,OAGnBE,oCAuCGC,OAAP,SAAuB/C,OACPgB,EAA0ChB,EAA1CgB,WAA0ChB,EAAhC2C,OAAAA,kBAAgC3C,EAAhBF,KAAAA,aAAO,OAG/C,OAAO2C,EAAgBjC,KAHiCR,EAAhDD,KAG2B,CAAED,KADrBR,EAAcQ,IAASA,EAAKA,KAAO,KAAOA,GACJ6C,IAAU3B,wCArChE,OAAOG,iCAGP,OAAOC,IAAOf,IAAIG,KAAKqC,SAASzB,mCAGhC,OAAOE,IAAOjB,IAAIG,KAAKqC,SAASvB,qCAGhC,OAAOE,IAASnB,IAAIG,KAAKqC,SAASrB,uCAGlC,OAAOE,IAASrB,IAAIG,KAAKqC,SAASnB,sCAGlC,OAAOE,IAAQvB,IAAIG,KAAKqC,SAASjB,sCAGjC,OAAOC,IAASxB,IAAIG,KAAKqC,SAAShB,uCAGlC,OAAOC,oCAGP,OAAOC,IAAU1B,IAAIG,KAAKqC,SAASd,2BAqBvBiB,EAAYC,SAC1B,gBAD0BA,IAAAA,EC9JyB,CACnD7B,KAAM,aACNE,MAAM,EACNE,OAAQ,GACRE,OAAQ,EACRE,MAAO,iBAAM,IACbC,OAAQ,iBAAO,IACfE,QAAS,oID4JL,YAAYvB,KAAKqC,wBAGSvC,GAS1BE,KAAKqC,UARK,IAANvC,QAIM,IAANA,EAIiBA,EAHE2C,GAJL,UATDH,kBACIG,KApDlBH,WAAsC,GA+BtCA,QAAQd,EACRc,SAASZ,EACTY,aAAaX,EACbW,YAAYT,EACZS,UAAUR,EACVQ,WAAWP,EACXO,QAAQN,EAORM,QAAQ,CACbI,OAAQpD,EACRkB,SAAU,8CA2Be,eAAzBmC,QAAQC,IAAIC,UACdC,QAAQC,KACN,iOAKiBC,iFAAiBR,KE7LtC9D,OAAOU,eAAe6D,QAAS,aAAc,CAC3C5D,OAAO"}