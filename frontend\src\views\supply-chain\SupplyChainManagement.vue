<template>
  <div class="supply-chain-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">供应链管理</h1>
          <p class="page-subtitle">统一管理供应商、采购、库存和物流全链条</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <PlusOutlined />
            </template>
            新建供应商
          </a-button>
        </div>
      </div>
    </div>

    <!-- 供应链概览 -->
    <div class="overview-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6" v-for="(card, index) in overviewCards" :key="index">
          <div class="overview-card" :class="`card-${index + 1}`">
            <div class="card-icon">
              <component :is="card.icon" />
            </div>
            <div class="card-content">
              <div class="card-value">{{ card.value }}</div>
              <div class="card-title">{{ card.title }}</div>
              <div class="card-trend" :class="card.trend">
                <component :is="card.trendIcon" />
                <span>{{ card.trendText }}</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 供应链流程 -->
    <div class="process-section">
      <div class="section-header">
        <h2>供应链流程</h2>
        <a-button type="text" @click="refreshProcessData">
          <ReloadOutlined />
          刷新
        </a-button>
      </div>
      
      <div class="process-flow">
        <div class="flow-step" v-for="(step, index) in processSteps" :key="index" :class="step.status">
          <div class="step-icon">
            <component :is="step.icon" />
          </div>
          <div class="step-content">
            <h4 class="step-title">{{ step.title }}</h4>
            <p class="step-description">{{ step.description }}</p>
            <div class="step-stats">
              <span class="stat-item">
                <span class="stat-label">进行中:</span>
                <span class="stat-value">{{ step.inProgress }}</span>
              </span>
              <span class="stat-item">
                <span class="stat-label">已完成:</span>
                <span class="stat-value">{{ step.completed }}</span>
              </span>
            </div>
          </div>
          <div class="step-actions">
            <a-button type="text" size="small" @click="handleStepDetail(step)">
              详情
            </a-button>
          </div>
          <div class="flow-arrow" v-if="index < processSteps.length - 1">
            <RightOutlined />
          </div>
        </div>
      </div>
    </div>

    <!-- 供应商管理 -->
    <div class="suppliers-section">
      <div class="section-header">
        <h2>供应商管理</h2>
        <div class="header-controls">
          <a-input-search
            placeholder="搜索供应商..."
            style="width: 250px; margin-right: 16px"
            @search="handleSupplierSearch"
          />
          <a-select
            v-model:value="supplierFilter"
            placeholder="筛选状态"
            style="width: 120px"
            @change="handleSupplierFilter"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="inactive">停用</a-select-option>
            <a-select-option value="pending">待审核</a-select-option>
          </a-select>
        </div>
      </div>
      
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="8" v-for="supplier in filteredSuppliers" :key="supplier.id">
          <div class="supplier-card">
            <div class="supplier-header">
              <div class="supplier-avatar">
                <a-avatar :size="48" :style="{ backgroundColor: supplier.color }">
                  {{ supplier.name.charAt(0) }}
                </a-avatar>
              </div>
              <div class="supplier-info">
                <h4 class="supplier-name">{{ supplier.name }}</h4>
                <p class="supplier-category">{{ supplier.category }}</p>
              </div>
              <div class="supplier-status">
                <a-tag :color="supplier.statusColor">{{ supplier.statusText }}</a-tag>
              </div>
            </div>
            
            <div class="supplier-content">
              <div class="supplier-stats">
                <div class="stat-row">
                  <span class="stat-label">合作时长:</span>
                  <span class="stat-value">{{ supplier.cooperationTime }}</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">订单总数:</span>
                  <span class="stat-value">{{ supplier.totalOrders }}</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">交付准时率:</span>
                  <span class="stat-value">{{ supplier.deliveryRate }}%</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">质量评分:</span>
                  <span class="stat-value">
                    <a-rate :value="supplier.qualityScore" disabled :count="5" />
                  </span>
                </div>
              </div>
            </div>
            
            <div class="supplier-actions">
              <a-button type="text" size="small" @click="handleSupplierDetail(supplier)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="text" size="small" @click="handleSupplierEdit(supplier)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="text" size="small" @click="handleSupplierOrder(supplier)">
                <ShoppingCartOutlined />
                下单
              </a-button>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 最近订单 -->
    <div class="orders-section">
      <div class="orders-card">
        <div class="orders-header">
          <h3>最近采购订单</h3>
          <a-button type="text" size="small" @click="router.push('/supply-chain/orders')">
            查看全部
          </a-button>
        </div>
        <a-table
          :columns="orderColumns"
          :data-source="recentOrders"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getOrderStatusColor(record.status)">
                {{ record.statusText }}
              </a-tag>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button type="text" size="small" @click="handleOrderDetail(record)">
                  查看
                </a-button>
                <a-button type="text" size="small" @click="handleOrderTrack(record)">
                  跟踪
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 创建供应商模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="新建供应商"
      width="600px"
      @ok="handleCreateSupplier"
      @cancel="showCreateModal = false"
    >
      <a-form :model="createForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="供应商名称" required>
              <a-input v-model:value="createForm.name" placeholder="请输入供应商名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商类型" required>
              <a-select v-model:value="createForm.category" placeholder="请选择类型">
                <a-select-option value="material">原材料供应商</a-select-option>
                <a-select-option value="equipment">设备供应商</a-select-option>
                <a-select-option value="service">服务供应商</a-select-option>
                <a-select-option value="logistics">物流供应商</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="联系人">
              <a-input v-model:value="createForm.contact" placeholder="请输入联系人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话">
              <a-input v-model:value="createForm.phone" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="地址">
          <a-input v-model:value="createForm.address" placeholder="请输入详细地址" />
        </a-form-item>
        
        <a-form-item label="备注">
          <a-textarea v-model:value="createForm.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  ReloadOutlined,
  RightOutlined,
  EyeOutlined,
  EditOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const showCreateModal = ref(false)
const supplierFilter = ref('')

// 创建表单
const createForm = reactive({
  name: '',
  category: '',
  contact: '',
  phone: '',
  address: '',
  remark: ''
})

// 概览卡片数据
const overviewCards = ref([
  {
    title: '活跃供应商',
    value: '156',
    icon: ShopOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+12 较上月'
  },
  {
    title: '待处理订单',
    value: '23',
    icon: ClockCircleOutlined,
    trend: 'down',
    trendIcon: ArrowDownOutlined,
    trendText: '-5 较昨日'
  },
  {
    title: '库存周转率',
    value: '85%',
    icon: InboxOutlined,
    trend: 'up',
    trendIcon: ArrowUpOutlined,
    trendText: '+3% 较上月'
  },
  {
    title: '采购成本',
    value: '¥1.2M',
    icon: DollarOutlined,
    trend: 'down',
    trendIcon: ArrowDownOutlined,
    trendText: '-8% 较上月'
  }
])

// 供应链流程步骤
const processSteps = ref([
  {
    title: '需求计划',
    description: '制定采购需求和计划',
    icon: ShopOutlined,
    status: 'active',
    inProgress: 12,
    completed: 45
  },
  {
    title: '供应商选择',
    description: '筛选和评估供应商',
    icon: TeamOutlined,
    status: 'active',
    inProgress: 8,
    completed: 23
  },
  {
    title: '采购执行',
    description: '下单和采购执行',
    icon: ShoppingCartOutlined,
    status: 'warning',
    inProgress: 15,
    completed: 67
  },
  {
    title: '物流配送',
    description: '货物运输和配送',
    icon: '',
    status: 'active',
    inProgress: 6,
    completed: 34
  },
  {
    title: '入库验收',
    description: '货物验收和入库',
    icon: InboxOutlined,
    status: 'active',
    inProgress: 3,
    completed: 28
  }
])

// 供应商数据
const suppliers = ref([
  {
    id: 1,
    name: '华为技术有限公司',
    category: '设备供应商',
    color: '#1890ff',
    status: 'active',
    statusText: '活跃',
    statusColor: 'green',
    cooperationTime: '3年',
    totalOrders: 156,
    deliveryRate: 98,
    qualityScore: 4.8
  },
  {
    id: 2,
    name: '中国移动通信',
    category: '服务供应商',
    color: '#52c41a',
    status: 'active',
    statusText: '活跃',
    statusColor: 'green',
    cooperationTime: '5年',
    totalOrders: 89,
    deliveryRate: 95,
    qualityScore: 4.6
  },
  {
    id: 3,
    name: '顺丰速运',
    category: '物流供应商',
    color: '#faad14',
    status: 'active',
    statusText: '活跃',
    statusColor: 'green',
    cooperationTime: '2年',
    totalOrders: 234,
    deliveryRate: 99,
    qualityScore: 4.9
  },
  {
    id: 4,
    name: '钢铁集团',
    category: '原材料供应商',
    color: '#722ed1',
    status: 'inactive',
    statusText: '停用',
    statusColor: 'red',
    cooperationTime: '1年',
    totalOrders: 45,
    deliveryRate: 85,
    qualityScore: 3.8
  }
])

// 最近订单数据
const recentOrders = ref([
  {
    id: 'PO2024001',
    supplier: '华为技术有限公司',
    amount: '¥125,000',
    status: 'pending',
    statusText: '待确认',
    createDate: '2024-01-15',
    deliveryDate: '2024-01-25'
  },
  {
    id: 'PO2024002',
    supplier: '顺丰速运',
    amount: '¥8,500',
    status: 'confirmed',
    statusText: '已确认',
    createDate: '2024-01-14',
    deliveryDate: '2024-01-20'
  },
  {
    id: 'PO2024003',
    supplier: '中国移动通信',
    amount: '¥45,000',
    status: 'shipped',
    statusText: '已发货',
    createDate: '2024-01-12',
    deliveryDate: '2024-01-18'
  }
])

// 订单表格列配置
const orderColumns = [
  {
    title: '订单号',
    dataIndex: 'id',
    key: 'id'
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    key: 'supplier'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '创建日期',
    dataIndex: 'createDate',
    key: 'createDate'
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 筛选后的供应商
const filteredSuppliers = computed(() => {
  if (!supplierFilter.value) return suppliers.value
  return suppliers.value.filter(supplier => supplier.status === supplierFilter.value)
})

// 事件处理函数
const refreshProcessData = () => {
  console.log('刷新流程数据')
}

const handleStepDetail = (step) => {
  console.log('查看步骤详情:', step)
}

const handleSupplierSearch = (value) => {
  console.log('搜索供应商:', value)
}

const handleSupplierFilter = (value) => {
  console.log('筛选供应商:', value)
}

const handleSupplierDetail = (supplier) => {
  console.log('查看供应商详情:', supplier)
}

const handleSupplierEdit = (supplier) => {
  console.log('编辑供应商:', supplier)
}

const handleSupplierOrder = (supplier) => {
  console.log('向供应商下单:', supplier)
}

const handleOrderDetail = (order) => {
  console.log('查看订单详情:', order)
}

const handleOrderTrack = (order) => {
  console.log('跟踪订单:', order)
}

const getOrderStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    confirmed: 'blue',
    shipped: 'green',
    delivered: 'green',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const handleCreateSupplier = () => {
  console.log('创建供应商:', createForm)
  showCreateModal.value = false
}
</script>

<style scoped>
/* 供应链管理页面样式 */
.supply-chain-management {
  padding: 0;
}

/* 流程区域样式 */
.process-section {
  margin-bottom: 24px;
}

.process-flow {
  display: flex;
  gap: 24px;
  overflow-x: auto;
  padding: 20px 0;
}

.flow-step {
  min-width: 280px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.flow-step:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.flow-step.active {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
}

.flow-step.warning {
  border-color: #faad14;
  background: linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%);
}

.step-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.flow-step.active .step-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.3);
}

.flow-step.warning .step-icon {
  background: linear-gradient(135deg, #faad14, #ffc53d);
  box-shadow: 0 4px 16px rgba(250, 173, 20, 0.3);
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.step-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.step-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.flow-arrow {
  position: absolute;
  right: -36px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  z-index: 1;
}

/* 供应商区域样式 */
.suppliers-section {
  margin-bottom: 24px;
}

.supplier-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.supplier-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}

.supplier-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.supplier-avatar {
  flex-shrink: 0;
}

.supplier-info {
  flex: 1;
}

.supplier-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.supplier-category {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.supplier-status {
  flex-shrink: 0;
}

.supplier-content {
  flex: 1;
  margin-bottom: 20px;
}

.supplier-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.supplier-actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 订单区域样式 */
.orders-section {
  margin-bottom: 24px;
}

.orders-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.orders-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.12);
}

.orders-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f0f5ff 100%);
}

.orders-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .process-flow {
    gap: 16px;
  }

  .flow-step {
    min-width: 240px;
  }
}

@media (max-width: 768px) {
  .process-flow {
    flex-direction: column;
    gap: 16px;
  }

  .flow-step {
    min-width: auto;
  }

  .flow-arrow {
    display: none;
  }

  .supplier-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .supplier-actions {
    flex-direction: column;
  }
}
</style>
